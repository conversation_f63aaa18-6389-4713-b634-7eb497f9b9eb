<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet type="text/xsl" href="configuration.xsl"?>
<!--
  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License. See accompanying LICENSE file.
-->

<!-- Put site-specific property overrides in this file. -->

<configuration>
  <property>
    <name>configuration.service</name>
    <value>org.apache.hadoop.fs.NameServiceConfigurationService</value>
  </property>
  <property>
    <name>configuration.service.name.team.id</name>
    <value>CL7202</value>
  </property>
  <property>
    <name>dfs.client.failover.proxy.provider.tjwqstaging-hdd</name>
    <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
  </property>

  <property>
    <name>dfs.federation.client.is-router-fs.tjwqstaging-hdd</name>
    <value>true</value>
  </property>

  <property>
    <name>dfs.ha.namenodes.tjwqstaging-hdd</name>
    <value>host0,host1,host2,host3</value>
  </property>

  <property>
    <name>dfs.namenode.rpc-address.tjwqstaging-hdd.host0</name>
    <value>tj-hadoop-staging-zk02.kscn:12500</value>
  </property>

  <property>
    <name>dfs.namenode.rpc-address.tjwqstaging-hdd.host1</name>
    <value>tj-hadoop-staging-zk03.kscn:12500</value>
  </property>

  <property>
    <name>dfs.namenode.rpc-address.tjwqstaging-hdd.host2</name>
    <value>tj-hadoop-staging-zk04.kscn:12500</value>
  </property>

  <property>
    <name>dfs.namenode.rpc-address.tjwqstaging-hdd.host3</name>
    <value>tj-hadoop-staging-zk05.kscn:12500</value>
  </property>

  <property>
    <name>dfs.nameservices</name>
    <value>tjwqstaging-hdd</value>
  </property>

  <property>
    <name>dfs.namenode.kerberos.principal.pattern</name>
    <value>hdfs_{prc,srv,tst}/<EMAIL></value>
  </property>
  <property>
    <name>hadoop.security.authentication</name>
    <value>kerberos</value>
  </property>
</configuration>
