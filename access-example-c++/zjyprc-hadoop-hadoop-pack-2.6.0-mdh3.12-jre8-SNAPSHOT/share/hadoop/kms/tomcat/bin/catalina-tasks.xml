<?xml version="1.0"?>
<!--
 Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->
<!--
  XML file for importing Catalina ant tasks.
  <import file="${catalina.home}/bin/catalina-tasks.xml"/>
-->

<project name="catalina-tasks">
  <description>Catalina Ant Manager, JMX and JSPC Tasks</description>
  <!-- set catalina.home if it's not already set -->
  <dirname property="catalina.home.bin.dir" file="${ant.file.catalina-tasks}"/>
  <property name="catalina.home" value="${catalina.home.bin.dir}/.."/>
  <taskdef resource="org/apache/catalina/ant/catalina.tasks">
    <classpath>
      <fileset file="${catalina.home}/bin/tomcat-juli.jar"/>
      <fileset file="${catalina.home}/lib/jasper.jar"/>
      <fileset file="${catalina.home}/lib/jasper-el.jar"/>
      <fileset file="${catalina.home}/lib/el-api.jar"/>
      <fileset file="${catalina.home}/lib/jsp-api.jar"/>
      <fileset file="${catalina.home}/lib/servlet-api.jar"/>
      <fileset file="${catalina.home}/lib/catalina-ant.jar"/>
    </classpath>
  </taskdef>
  <taskdef resource="org/apache/catalina/ant/jmx/jmxaccessor.tasks">
    <classpath>
      <fileset file="${catalina.home}/lib/catalina-ant.jar"/>
    </classpath>
  </taskdef>
  <typedef
        name="jmxEquals"
        classname="org.apache.catalina.ant.jmx.JMXAccessorEqualsCondition">
    <classpath>
      <fileset file="${catalina.home}/lib/catalina-ant.jar"/>
    </classpath>
  </typedef>
  <typedef
        name="jmxCondition"
        classname="org.apache.catalina.ant.jmx.JMXAccessorCondition">
    <classpath>
      <fileset file="${catalina.home}/lib/catalina-ant.jar"/>
    </classpath>
  </typedef>
</project>
