<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License. See accompanying LICENSE file.
-->
<web-app version="2.4" xmlns="http://java.sun.com/xml/ns/j2ee">

<!--
Automatically created by Apache Jakarta Tomcat JspC.
Place this fragment in the web.xml before all icon, display-name,
description, distributable, and context-param elements.
-->

    <servlet>
        <servlet-name>org.apache.hadoop.hdfs.server.namenode.status_jsp</servlet-name>
        <servlet-class>org.apache.hadoop.hdfs.server.namenode.status_jsp</servlet-class>
    </servlet>

    <servlet-mapping>
        <servlet-name>org.apache.hadoop.hdfs.server.namenode.status_jsp</servlet-name>
        <url-pattern>/status.jsp</url-pattern>
    </servlet-mapping>

<!--
All session-config, mime-mapping, welcome-file-list, error-page, taglib,
resource-ref, security-constraint, login-config, security-role,
env-entry, and ejb-ref elements should follow this fragment.
-->

</web-app>
