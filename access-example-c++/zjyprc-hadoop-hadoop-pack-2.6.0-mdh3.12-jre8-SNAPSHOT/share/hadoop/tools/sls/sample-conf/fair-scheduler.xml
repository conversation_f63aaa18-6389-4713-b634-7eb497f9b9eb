<?xml version="1.0"?>
<!--
  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License. See accompanying LICENSE file.
-->

<!--
  This file contains pool and user allocations for the Fair Scheduler.
  Its format is explained in the Fair Scheduler documentation at
  http://hadoop.apache.org/docs/current/hadoop-yarn/hadoop-yarn-site/FairScheduler.html.
  The documentation also includes a sample config file.
-->

<allocations>
  <user name="jenkins">
    <!-- Limit on running jobs for the user across all pools. If more
      jobs than this are submitted, only the first <maxRunningJobs> will
      be scheduled at any given time. Defaults to infinity or the
      userMaxJobsDefault value set below. -->
    <maxRunningJobs>1000</maxRunningJobs>
  </user>
  <userMaxAppsDefault>1000</userMaxAppsDefault>
  <queue name="sls_queue_1">
    <minResources>1024 mb, 1 vcores</minResources>
    <schedulingMode>fair</schedulingMode>
    <weight>0.25</weight>
    <minSharePreemptionTimeout>2</minSharePreemptionTimeout>
  </queue>
  <queue name="sls_queue_2">
    <minResources>1024 mb, 1 vcores</minResources>
    <schedulingMode>fair</schedulingMode>
    <weight>0.25</weight>
    <minSharePreemptionTimeout>2</minSharePreemptionTimeout>
  </queue>
  <queue name="sls_queue_3">
    <minResources>1024 mb, 1 vcores</minResources>
    <weight>0.5</weight>
    <schedulingMode>fair</schedulingMode>
    <minSharePreemptionTimeout>2</minSharePreemptionTimeout>
  </queue>
</allocations>
