.DS_Store
*.iml
*.ipr
*.iws
*.orig
*.rej
**/.keep
*.sdf
*.suo
*.vcxproj.user
*.patch
.idea
.svn
.classpath
.project
.settings
target
build
dependency-reduced-pom.xml

# Filesystem contract test options and credentials
auth-keys.xml
azure-auth-keys.xml

# External tool builders
*/.externalToolBuilders
*/maven-eclipse.xml

hadoop-common-project/hadoop-kms/downloads/
hadoop-hdfs-project/hadoop-hdfs/downloads
hadoop-hdfs-project/hadoop-hdfs-httpfs/downloads
hadoop-yarn-project/hadoop-yarn/hadoop-yarn-registry/src/main/tla/yarnregistry.toolbox
hadoop-yarn-project/hadoop-yarn/hadoop-yarn-ui/src/main/webapp/dist
hadoop-yarn-project/hadoop-yarn/hadoop-yarn-ui/src/main/webapp/tmp
hadoop-yarn-project/hadoop-yarn/hadoop-yarn-ui/src/main/webapp/node
hadoop-yarn-project/hadoop-yarn/hadoop-yarn-ui/src/main/webapp/node_modules
hadoop-yarn-project/hadoop-yarn/hadoop-yarn-ui/src/main/webapp/bower_components
hadoop-yarn-project/hadoop-yarn/hadoop-yarn-ui/src/main/webapp/.sass-cache
hadoop-yarn-project/hadoop-yarn/hadoop-yarn-ui/src/main/webapp/connect.lock
hadoop-yarn-project/hadoop-yarn/hadoop-yarn-ui/src/main/webapp/coverage/*
hadoop-yarn-project/hadoop-yarn/hadoop-yarn-ui/src/main/webapp/libpeerconnection.log
hadoop-yarn-project/hadoop-yarn/hadoop-yarn-ui/src/main/webapp/npm-debug.log
hadoop-yarn-project/hadoop-yarn/hadoop-yarn-ui/src/main/webapp/testem.log
hadoop-yarn-project/hadoop-yarn/hadoop-yarn-ui/dist
hadoop-yarn-project/hadoop-yarn/hadoop-yarn-ui/tmp
yarnregistry.pdf
patchprocess/
.history/
hadoop-yarn-project/hadoop-yarn/hadoop-yarn-ui/src/main/webapp/package-lock.json
hadoop-yarn-project/hadoop-yarn/hadoop-yarn-ui/src/main/webapp/yarn-error.log
