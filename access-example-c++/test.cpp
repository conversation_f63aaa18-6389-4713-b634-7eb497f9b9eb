// file name : test.cpp
#include "jni.h"
#include "hdfs.h"
#include <stdlib.h>
#include <string.h>

int main(int argc, char **argv)
{
  hdfsFS fs = hdfsConnect("hdfs://tjwqstaging-hdd", 0);
  const char* writePath = "/tmp/u_gyc/testfile.txt";
  hdfsFile writeFile = hdfsOpenFile(fs, writePath, O_WRONLY|O_CREAT, 0, 0, 0);
  if(!writeFile) {
    fprintf(stderr, "Failed to open %s for writing!\n", writePath);
    exit(-1);
  }
  const char* buffer = "Hello, World!";
  tSize num_written_bytes = hdfsWrite(fs, writeFile, (void*)buffer, strlen(buffer)+1);
  if (hdfsFlush(fs, writeFile)) {
    fprintf(stderr, "Failed to 'flush' %s\n", writePath); 
    exit(-1);
  }
  printf("the file size is %lld\n", (long long)num_written_bytes);

  hdfsCloseFile(fs, writeFile);
  return 0;
}
