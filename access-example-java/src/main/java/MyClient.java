import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.Path;

import java.io.BufferedOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URISyntaxException;

/**
 * 接入HDFS
 * */
public class MyClient {

  public void visitHDFS(String[] args) throws IOException {
    Configuration conf = new Configuration();

    Path path = new Path(args[0]);
    FileSystem fs = FileSystem.get(path.toUri(), conf);

    if(!fs.exists(path)) {
      BufferedOutputStream bos =
          new BufferedOutputStream(fs.create(path, false));
      try {
        byte[] bytes = "hello world\n".getBytes();
        bos.write(bytes);
      } finally {
        if (bos != null) {
          bos.close();
        }
      }
    }

    InputStream in = fs.open(path);
    StringBuilder res = new StringBuilder();
    byte[] buffer = new byte[1024];
    int read;
    while((read = in.read(buffer)) > 0) {
      res.append(new String(buffer, 0, read));
    }
    System.out.println(res.toString());
    in.close();
  }
  public static void main(String args[]) throws IOException, URISyntaxException {
    new MyClient().visitHDFS(new String[]{"hdfs://tjwqstaging-hdd/tmp"});
  }
}