import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.Path;

import java.io.BufferedOutputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * HDFS接入例子，写文件到HDFS再读取。
 */
public class MyClient {

  public void visitHDFS(String[] args) throws IOException {
    Configuration conf = new Configuration();

    Path path = new Path(args[0]);
    FileSystem fs = FileSystem.get(path.toUri(), conf);

    BufferedOutputStream bos = new BufferedOutputStream(fs.create(path, true));
    try {
      byte[] bytes = "hello world\n".getBytes();
      bos.write(bytes);
    } finally {
      if (bos != null) {
        bos.close();
      }
    }

    InputStream in = fs.open(path);
    StringBuilder res = new StringBuilder();
    byte[] buffer = new byte[1024];
    int read;
    while((read = in.read(buffer)) > 0) {
      res.append(new String(buffer, 0, read));
    }
    System.out.println(res.toString());
    in.close();
  }

  public static void main(String args[]) throws IOException {
    if (args == null || args.length == 0) {
      // 替换{your_name}为你自己的路径，避免和其他人冲突。
      args = new String[]{"hdfs://tjwqstaging-hdd/tmp/{your_name}/test-file"};
    }
    new MyClient().visitHDFS(args);
  }
}