<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet type="text/xsl" href="configuration.xsl"?>
<!--
  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License. See accompanying LICENSE file.
-->

<!-- Put site-specific property overrides in this file. -->

<configuration>

  <property>
    <name>fs.defaultFS</name>
    <value>hdfs://tjwqstaging-hdd</value>
  </property>

  <property>
    <name>ha.zookeeper.quorum</name>
    <value>tj-hadoop-staging-zk01.kscn:11000,tj-hadoop-staging-zk02.kscn:11000,tj-hadoop-staging-zk03.kscn:11000,tj-hadoop-staging-zk04.kscn:11000,tj-hadoop-staging-zk05.kscn:11000</value>
  </property>

  <property>
    <name>hadoop.http.staticuser.user</name>
    <value>yarn</value>
  </property>

  <property>
    <name>hadoop.proxyuser.hive_prc.groups</name>
    <value>*</value>
  </property>

  <property>
    <name>hadoop.proxyuser.hive_prc.hosts</name>
    <value>*</value>
  </property>

  <property>
    <name>hadoop.proxyuser.hive_tst.groups</name>
    <value>*</value>
  </property>

  <property>
    <name>hadoop.proxyuser.hive_tst.hosts</name>
    <value>*</value>
  </property>

  <property>
    <name>hadoop.proxyuser.hue.groups</name>
    <value>*</value>
  </property>

  <property>
    <name>hadoop.proxyuser.hue.hosts</name>
    <value>*</value>
  </property>

  <property>
    <name>hadoop.security.authentication</name>
    <value>kerberos</value>
  </property>

  <property>
    <name>hadoop.security.authorization</name>
    <value>true</value>
  </property>

  <property>
    <name>hadoop.security.use-weak-http-crypto</name>
    <value>false</value>
  </property>

  <property>
    <name>hadoop.tmp.dir</name>
    <value>/tmp/hadoop</value>
  </property>

  <property>
    <name>io.file.buffer.size</name>
    <value>131072</value>
  </property>

  <property>
    <name>ipc.client.ping</name>
    <value>false</value>
  </property>

  <property>
    <name>net.topology.node.switch.mapping.impl</name>
    <value>org.apache.hadoop.net.TableMapping</value>
  </property>

  <property>
    <name>net.topology.table.file.name</name>
    <value>/home/<USER>/app/yarn/tjwqstaging-hdd/resourcemanager/rackinfo.txt</value>
  </property>

  <property>
    <name>hadoop.client.kerberos.principal</name>
    <value><EMAIL></value>
  </property>
  <property>
    <name>hadoop.client.keytab.file</name>
    <value>/home/<USER>/hdfs_tst_admin.keytab</value>
  </property>


</configuration>
