<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet type="text/xsl" href="configuration.xsl"?>
<!--
  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->
<configuration>

  <!-- HttpFSServer Server -->

  <property>
    <name>httpfs.buffer.size</name>
    <value>4096</value>
    <description>
      The buffer size used by a read/write request when streaming data from/to
      HDFS.
    </description>
  </property>

  <!-- HttpFSServer Services -->

  <property>
    <name>httpfs.services</name>
    <value>
      org.apache.hadoop.lib.service.instrumentation.InstrumentationService,
      org.apache.hadoop.lib.service.scheduler.SchedulerService,
      org.apache.hadoop.lib.service.security.GroupsService,
      org.apache.hadoop.lib.service.hadoop.FileSystemAccessService
    </value>
    <description>
      Services used by the httpfs server.
    </description>
  </property>

  <!-- Kerberos Configuration -->

  <property>
    <name>kerberos.realm</name>
    <value>LOCALHOST</value>
    <description>
      Kerberos realm, used only if Kerberos authentication is used between
      the clients and httpfs or between HttpFS and HDFS.

      This property is only used to resolve other properties within this
      configuration file.
    </description>
  </property>

  <!-- HttpFSServer Security Configuration -->

  <property>
    <name>httpfs.hostname</name>
    <value>${httpfs.http.hostname}</value>
    <description>
      Property used to synthetize the HTTP Kerberos principal used by httpfs.

      This property is only used to resolve other properties within this
      configuration file.
    </description>
  </property>

  <property>
    <name>httpfs.authentication.signature.secret.file</name>
    <value>${httpfs.config.dir}/httpfs-signature.secret</value>
    <description>
      File containing the secret to sign HttpFS hadoop-auth cookies.

      This file should be readable only by the system user running HttpFS service.

      If multiple HttpFS servers are used in a load-balancer/round-robin fashion,
      they should share the secret file.
    </description>
  </property>

  <property>
    <name>httpfs.authentication.type</name>
    <value>simple</value>
    <description>
      Defines the authentication mechanism used by httpfs for its HTTP clients.

      Valid values are 'simple' or 'kerberos'.

      If using 'simple' HTTP clients must specify the username with the
      'user.name' query string parameter.

      If using 'kerberos' HTTP clients must use HTTP SPNEGO or delegation tokens.
    </description>
  </property>

  <property>
    <name>httpfs.authentication.kerberos.principal</name>
    <value>HTTP/${httpfs.hostname}@${kerberos.realm}</value>
    <description>
      The HTTP Kerberos principal used by HttpFS in the HTTP endpoint.

      The HTTP Kerberos principal MUST start with 'HTTP/' per Kerberos
      HTTP SPNEGO specification.
    </description>
  </property>

  <property>
    <name>httpfs.authentication.kerberos.keytab</name>
    <value>${user.home}/httpfs.keytab</value>
    <description>
      The Kerberos keytab file with the credentials for the
      HTTP Kerberos principal used by httpfs in the HTTP endpoint.
    </description>
  </property>

  <!-- HttpFSServer proxy user Configuration -->
<!--

  The following 2 properties within this comment are provided as an
  example to facilitate configuring HttpFS proxyusers.

  <property>
    <name>httpfs.proxyuser.#USER#.hosts</name>
    <value>*</value>
    <description>
      List of hosts the '#USER#' user is allowed to perform 'doAs'
      operations.

      The '#USER#' must be replaced with the username o the user who is
      allowed to perform 'doAs' operations.

      The value can be the '*' wildcard or a list of hostnames.

      For multiple users copy this property and replace the user name
      in the property name.
    </description>
  </property>

  <property>
    <name>httpfs.proxyuser.#USER#.groups</name>
    <value>*</value>
    <description>
      List of groups the '#USER#' user is allowed to impersonate users
      from to perform 'doAs' operations.

      The '#USER#' must be replaced with the username o the user who is
      allowed to perform 'doAs' operations.

      The value can be the '*' wildcard or a list of groups.

      For multiple users copy this property and replace the user name
      in the property name.
    </description>
  </property>
-->

  <!-- HttpFS Delegation Token configuration -->

  <property>
    <name>httpfs.delegation.token.manager.update.interval</name>
    <value>86400</value>
    <description>
      HttpFS delegation token update interval, default 1 day, in seconds.
    </description>
  </property>

  <property>
    <name>httpfs.delegation.token.manager.max.lifetime</name>
    <value>604800</value>
    <description>
      HttpFS delegation token maximum lifetime, default 7 days, in seconds
    </description>
  </property>

  <property>
    <name>httpfs.delegation.token.manager.renewal.interval</name>
    <value>86400</value>
    <description>
      HttpFS delegation token update interval, default 1 day, in seconds.
    </description>
  </property>

  <!-- FileSystemAccess Namenode Security Configuration -->

  <property>
    <name>httpfs.hadoop.authentication.type</name>
    <value>simple</value>
    <description>
      Defines the authentication mechanism used by httpfs to connect to
      the HDFS Namenode.

      Valid values are 'simple' and 'kerberos'.
    </description>
  </property>

  <property>
    <name>httpfs.hadoop.authentication.kerberos.keytab</name>
    <value>${user.home}/httpfs.keytab</value>
    <description>
      The Kerberos keytab file with the credentials for the
      Kerberos principal used by httpfs to connect to the HDFS Namenode.
    </description>
  </property>

  <property>
    <name>httpfs.hadoop.authentication.kerberos.principal</name>
    <value>${user.name}/${httpfs.hostname}@${kerberos.realm}</value>
    <description>
      The Kerberos principal used by httpfs to connect to the HDFS Namenode.
    </description>
  </property>

  <property>
    <name>httpfs.hadoop.filesystem.cache.purge.frequency</name>
    <value>60</value>
    <description>
      Frequency, in seconds, for the idle filesystem purging daemon runs.
    </description>
  </property>

  <property>
    <name>httpfs.hadoop.filesystem.cache.purge.timeout</name>
    <value>60</value>
    <description>
      Timeout, in seconds, for an idle filesystem to be purged.
    </description>
  </property>

</configuration>
