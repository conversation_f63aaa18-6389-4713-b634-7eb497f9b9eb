# file name: exec.sh
export HADOOP_ROOT=/home/<USER>/program/hdfs-example3/zjyprc-hadoop-hadoop-pack-2.6.0-mdh3.12-jre8-SNAPSHOT

export CXXFLAGS="-O2 -g -Wall -I$HADOOP_ROOT/include/ -I$JAVA_HOME/include/ -I$JAVA_HOME/include/linux/"
export LDFLAGS="-L$HADOOP_ROOT/lib/native/ -lhdfs -L$JAVA_HOME/jre/lib/amd64/server/ -ljvm"
export LD_LIBRARY_PATH="$HADOOP_ROOT/lib/native/:$JAVA_HOME/jre/lib/amd64/server/"
export CLASSPATH=$CLASSPATH:$HADOOP_ROOT/etc/hadoop/:$HADOOP_ROOT/share/hadoop/common/:$HADOOP_ROOT/share/hadoop/hdfs/

if [ -d $HADOOP_ROOT/share/hadoop ]; then
 for f in $HADOOP_ROOT/share/hadoop/common/lib/*.jar; do
   export CLASSPATH=$CLASSPATH:$f
 done
 for f in $HADOOP_ROOT/share/hadoop/common/*.jar; do
   export CLASSPATH=$CLASSPATH:$f
 done
 for f in $HADOOP_ROOT/share/hadoop/hdfs/lib/*.jar; do
   export CLASSPATH=$CLASSPATH:$f
 done
 for f in $HADOOP_ROOT/share/hadoop/hdfs/*.jar; do
   export CLASSPATH=$CLASSPATH:$f
 done
 for f in $HADOOP_ROOT/share/hadoop/yarn/lib/*.jar; do
   export CLASSPATH=$CLASSPATH:$f
 done
 for f in $HADOOP_ROOT/share/hadoop/yarn/*.jar; do
   export CLASSPATH=$CLASSPATH:$f
 done
 for f in $HADOOP_ROOT/share/hadoop/mapreduce/lib/*.jar; do
   export CLASSPATH=$CLASSPATH:$f
 done
 for f in $HADOOP_ROOT/share/hadoop/mapreduce/*.jar; do
   export CLASSPATH=$CLASSPATH:$f
 done
fi

#(echo r ; cat) | gdb a.out
strace -o output ./a.out
