<configuration>
  <property>
    <name>configuration.service</name>
    <value>org.apache.hadoop.fs.NameServiceConfigurationService</value>
  </property>
  <property>
    <name>configuration.service.name.team.id</name>
    <value>CL7202</value>
  </property>
  <property>
    <name>dfs.blocksize</name>
    <value>256m</value>
  </property> 
  <property>
    <name>dfs.federation.client.is-router-fs.ksmosprc-hadoop</name>
    <value>true</value>
  </property>
  <property>
    <name>dfs.ha.namenodes.ksmosprc-hadoop</name>
    <value>host0,host1,host2,host3,host4,host5,host6,host7,host8,host9,host10,host11</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.ksmosprc-hadoop.host0</name>
    <value>mos1-hadoop-prc-ct01.ksru:11500</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.ksmosprc-hadoop.host1</name>
    <value>mos1-hadoop-prc-ct02.ksru:11500</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.ksmosprc-hadoop.host2</name>
    <value>mos1-hadoop-prc-ct03.ksru:11500</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.ksmosprc-hadoop.host3</name>
    <value>mos1-hadoop-prc-ct04.ksru:11500</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.ksmosprc-hadoop.host4</name>
    <value>mos1-hadoop-prc-ct05.ksru:11500</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.ksmosprc-hadoop.host5</name>
    <value>mos1-hadoop-prc-ct06.ksru:11500</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.ksmosprc-hadoop.host6</name>
    <value>mos1-hadoop-prc-ct07.ksru:11500</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.ksmosprc-hadoop.host7</name>
    <value>mos1-hadoop-prc-ct08.ksru:11500</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.ksmosprc-hadoop.host8</name>
    <value>mos1-hadoop-prc-ct09.ksru:11500</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.ksmosprc-hadoop.host9</name>
    <value>mos1-hadoop-prc-ct10.ksru:11500</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.ksmosprc-hadoop.host10</name>
    <value>mos1-hadoop-prc-ct11.ksru:11500</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.ksmosprc-hadoop.host11</name>
    <value>mos1-hadoop-prc-ct12.ksru:11500</value>
  </property>
  <property>
    <name>dfs.client.failover.proxy.provider.ksmosprc-hadoop</name>
    <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
  </property>
  <property>
    <name>dfs.nameservices</name>
    <value>ksmosprc-hadoop</value>
  </property>
  <property>
    <name>dfs.namenode.kerberos.principal.pattern</name>
    <value>hdfs_{prc,srv,tst}/<EMAIL></value>
  </property>
  <property>
    <name>hadoop.security.authentication</name>
    <value>kerberos</value>
  </property>
  <property>
    <name>dfs.client.failover.random.order</name>
    <value>true</value>
  </property>
</configuration>
