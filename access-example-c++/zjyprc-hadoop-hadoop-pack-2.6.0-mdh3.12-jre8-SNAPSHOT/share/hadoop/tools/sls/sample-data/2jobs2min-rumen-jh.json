{
  "priority" : "NORMAL",
  "jobID" : "job_1369942127770_1205",
  "user" : "jenkins",
  "jobName" : "TeraGen",
  "submitTime" : 1371222054499,
  "finishTime" : 1371222153874,
  "queue" : "sls_queue_1",
  "mapTasks" : [ {
    "startTime" : 1371222059053,
    "taskID" : "task_1369942127770_1205_m_000000",
    "taskType" : "MAP",
    "finishTime" : 1371222078206,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2118.smile.com" ]
      },
      "hostName" : "/default-rack/a2118.smile.com",
      "startTime" : 1371222061163,
      "result" : "SUCCESS",
      "finishTime" : 1371222078206,
      "attemptID" : "attempt_1369942127770_1205_m_000000_0",
      "clockSplits" : [ 1481, 1481, 1481, 1481, 1481, 1481, 1169, 1064, 1064, 1173, 1197, 2485 ],
      "cpuUsages" : [ 1253, 1254, 1254, 1254, 1254, 1254, 1198, 1180, 1179, 984, 942, 1294 ],
      "vmemKbytes" : [ 113886, 341661, 569436, 797210, 1024985, 1252759, 1416845, 1424087, 1424088, 1424088, 1424087, 1424088 ],
      "physMemKbytes" : [ 24766, 74299, 123833, 173366, 222900, 272433, 308230, 310197, 310604, 309580, 305826, 302119 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 82,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72234,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14300,
        "virtualMemoryUsage" : 1458266112,
        "physicalMemoryUsage" : 308215808,
        "heapUsage" : 745406464
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 82,
    "inputRecords" : 4166667,
    "outputBytes" : 72234,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059055,
    "taskID" : "task_1369942127770_1205_m_000001",
    "taskType" : "MAP",
    "finishTime" : 1371222076092,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2118.smile.com" ]
      },
      "hostName" : "/default-rack/a2118.smile.com",
      "startTime" : 1371222061164,
      "result" : "SUCCESS",
      "finishTime" : 1371222076092,
      "attemptID" : "attempt_1369942127770_1205_m_000001_0",
      "clockSplits" : [ 1621, 1621, 1622, 1621, 1622, 1457, 908, 908, 908, 2328, 150, 151 ],
      "cpuUsages" : [ 1306, 1307, 1307, 1306, 1307, 1264, 1119, 1119, 1119, 1020, 993, 993 ],
      "vmemKbytes" : [ 122129, 366389, 610648, 854907, 1099167, 1336974, 1409412, 1409411, 1409412, 1409412, 1409411, 1409412 ],
      "physMemKbytes" : [ 27010, 81032, 135053, 189074, 243096, 295613, 309560, 306611, 303663, 300925, 298639, 296368 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 85,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72234,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14160,
        "virtualMemoryUsage" : 1443237888,
        "physicalMemoryUsage" : 302317568,
        "heapUsage" : 745209856
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 85,
    "inputRecords" : 4166667,
    "outputBytes" : 72234,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059055,
    "taskID" : "task_1369942127770_1205_m_000002",
    "taskType" : "MAP",
    "finishTime" : 1371222076916,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2118.smile.com" ]
      },
      "hostName" : "/default-rack/a2118.smile.com",
      "startTime" : 1371222061159,
      "result" : "SUCCESS",
      "finishTime" : 1371222076916,
      "attemptID" : "attempt_1369942127770_1205_m_000002_0",
      "clockSplits" : [ 1529, 1530, 1530, 1530, 1529, 1530, 1072, 975, 975, 2938, 269, 269 ],
      "cpuUsages" : [ 1266, 1266, 1267, 1266, 1267, 1266, 1172, 1152, 1151, 1056, 995, 996 ],
      "vmemKbytes" : [ 115166, 345498, 575831, 806163, 1036496, 1266828, 1418750, 1422271, 1422272, 1422272, 1422271, 1422272 ],
      "physMemKbytes" : [ 25182, 75547, 125913, 176278, 226644, 277009, 311047, 314183, 316587, 318060, 315880, 313328 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 85,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72234,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14260,
        "virtualMemoryUsage" : 1456406528,
        "physicalMemoryUsage" : 319447040,
        "heapUsage" : 740294656
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 85,
    "inputRecords" : 4166666,
    "outputBytes" : 72234,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222059055,
    "taskID" : "task_1369942127770_1205_m_000003",
    "taskType" : "MAP",
    "finishTime" : 1371222078156,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2118.smile.com" ]
      },
      "hostName" : "/default-rack/a2118.smile.com",
      "startTime" : 1371222061160,
      "result" : "SUCCESS",
      "finishTime" : 1371222078156,
      "attemptID" : "attempt_1369942127770_1205_m_000003_0",
      "clockSplits" : [ 1473, 1473, 1473, 1473, 1474, 1473, 1250, 1064, 1065, 2986, 293, 294 ],
      "cpuUsages" : [ 1227, 1227, 1227, 1227, 1227, 1228, 1214, 1202, 1203, 1076, 966, 966 ],
      "vmemKbytes" : [ 111579, 334737, 557896, 781055, 1004214, 1227373, 1417243, 1440219, 1440220, 1440220, 1440219, 1440220 ],
      "physMemKbytes" : [ 24240, 72721, 121202, 169683, 218165, 266646, 308296, 315697, 318384, 320500, 319654, 318383 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 85,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72234,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14300,
        "virtualMemoryUsage" : 1474785280,
        "physicalMemoryUsage" : 325296128,
        "heapUsage" : 744161280
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 85,
    "inputRecords" : 4166667,
    "outputBytes" : 72234,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059056,
    "taskID" : "task_1369942127770_1205_m_000004",
    "taskType" : "MAP",
    "finishTime" : 1371222077984,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2118.smile.com" ]
      },
      "hostName" : "/default-rack/a2118.smile.com",
      "startTime" : 1371222061161,
      "result" : "SUCCESS",
      "finishTime" : 1371222077984,
      "attemptID" : "attempt_1369942127770_1205_m_000004_0",
      "clockSplits" : [ 1441, 1441, 1441, 1441, 1441, 1441, 1297, 1139, 1139, 3202, 106, 107 ],
      "cpuUsages" : [ 1231, 1231, 1231, 1231, 1231, 1231, 1241, 1253, 1252, 1018, 920, 920 ],
      "vmemKbytes" : [ 110202, 330607, 551012, 771417, 991823, 1212228, 1407542, 1437667, 1437668, 1437668, 1437667, 1437668 ],
      "physMemKbytes" : [ 23865, 71596, 119328, 167059, 214790, 262521, 305059, 313396, 315497, 316328, 313564, 310583 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 85,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72234,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14350,
        "virtualMemoryUsage" : 1472172032,
        "physicalMemoryUsage" : 316485632,
        "heapUsage" : 740163584
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 85,
    "inputRecords" : 4166667,
    "outputBytes" : 72234,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059056,
    "taskID" : "task_1369942127770_1205_m_000005",
    "taskType" : "MAP",
    "finishTime" : 1371222078263,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2118.smile.com" ]
      },
      "hostName" : "/default-rack/a2118.smile.com",
      "startTime" : 1371222061161,
      "result" : "SUCCESS",
      "finishTime" : 1371222078263,
      "attemptID" : "attempt_1369942127770_1205_m_000005_0",
      "clockSplits" : [ 1467, 1467, 1467, 1467, 1468, 1467, 1222, 1044, 1043, 3239, 138, 138 ],
      "cpuUsages" : [ 1228, 1229, 1228, 1229, 1229, 1228, 1187, 1157, 1157, 1036, 951, 951 ],
      "vmemKbytes" : [ 109716, 329150, 548584, 768017, 987451, 1206884, 1389627, 1409139, 1409140, 1409140, 1409139, 1409140 ],
      "physMemKbytes" : [ 24267, 72802, 121338, 169873, 218408, 266943, 307379, 311778, 311871, 311950, 311973, 311991 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72234,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14290,
        "virtualMemoryUsage" : 1442959360,
        "physicalMemoryUsage" : 319471616,
        "heapUsage" : 732299264
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72234,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222059056,
    "taskID" : "task_1369942127770_1205_m_000006",
    "taskType" : "MAP",
    "finishTime" : 1371222080381,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2115.smile.com" ]
      },
      "hostName" : "/default-rack/a2115.smile.com",
      "startTime" : 1371222061165,
      "result" : "SUCCESS",
      "finishTime" : 1371222080381,
      "attemptID" : "attempt_1369942127770_1205_m_000006_0",
      "clockSplits" : [ 2346, 2347, 2347, 2347, 1888, 1412, 1285, 962, 962, 962, 1936, 416 ],
      "cpuUsages" : [ 1386, 1386, 1386, 1386, 1216, 1041, 1052, 1082, 1082, 1081, 1185, 1217 ],
      "vmemKbytes" : [ 157018, 471054, 785090, 1099125, 1375327, 1416027, 1416046, 1416396, 1416871, 1417345, 1417679, 1417692 ],
      "physMemKbytes" : [ 32211, 96633, 161055, 225476, 282302, 291840, 293174, 293917, 294444, 294970, 293975, 289481 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72234,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14500,
        "virtualMemoryUsage" : 1451716608,
        "physicalMemoryUsage" : 294055936,
        "heapUsage" : 740032512
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72234,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059057,
    "taskID" : "task_1369942127770_1205_m_000007",
    "taskType" : "MAP",
    "finishTime" : 1371222080174,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2115.smile.com" ]
      },
      "hostName" : "/default-rack/a2115.smile.com",
      "startTime" : 1371222061168,
      "result" : "SUCCESS",
      "finishTime" : 1371222080174,
      "attemptID" : "attempt_1369942127770_1205_m_000007_0",
      "clockSplits" : [ 1912, 1913, 1913, 1913, 1913, 1395, 1079, 1080, 1110, 1121, 1122, 2528 ],
      "cpuUsages" : [ 1309, 1309, 1309, 1309, 1309, 1145, 1046, 1045, 1004, 988, 989, 1278 ],
      "vmemKbytes" : [ 133705, 401116, 668527, 935938, 1203349, 1418939, 1437988, 1437987, 1437988, 1437988, 1437987, 1437988 ],
      "physMemKbytes" : [ 29116, 87350, 145583, 203816, 262051, 309017, 313251, 313344, 312660, 309967, 307170, 305234 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72234,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14040,
        "virtualMemoryUsage" : 1472499712,
        "physicalMemoryUsage" : 312479744,
        "heapUsage" : 723451904
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72234,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059057,
    "taskID" : "task_1369942127770_1205_m_000008",
    "taskType" : "MAP",
    "finishTime" : 1371222080738,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2115.smile.com" ]
      },
      "hostName" : "/default-rack/a2115.smile.com",
      "startTime" : 1371222061150,
      "result" : "SUCCESS",
      "finishTime" : 1371222080738,
      "attemptID" : "attempt_1369942127770_1205_m_000008_0",
      "clockSplits" : [ 2192, 2193, 2192, 2193, 2192, 1226, 1140, 1210, 1721, 2853, 235, 236 ],
      "cpuUsages" : [ 1340, 1340, 1340, 1341, 1340, 1169, 1153, 1147, 1096, 1086, 1059, 1059 ],
      "vmemKbytes" : [ 140265, 420795, 701325, 981854, 1262385, 1424418, 1425336, 1425335, 1425336, 1425336, 1425335, 1425336 ],
      "physMemKbytes" : [ 29311, 87935, 146559, 205182, 263807, 298779, 301592, 304203, 305246, 305221, 304062, 302477 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72234,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14470,
        "virtualMemoryUsage" : 1459544064,
        "physicalMemoryUsage" : 308924416,
        "heapUsage" : 742719488
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72234,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222059057,
    "taskID" : "task_1369942127770_1205_m_000009",
    "taskType" : "MAP",
    "finishTime" : 1371222080294,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2115.smile.com" ]
      },
      "hostName" : "/default-rack/a2115.smile.com",
      "startTime" : 1371222061167,
      "result" : "SUCCESS",
      "finishTime" : 1371222080294,
      "attemptID" : "attempt_1369942127770_1205_m_000009_0",
      "clockSplits" : [ 2681, 2682, 2682, 2682, 931, 900, 900, 1034, 1187, 1187, 1891, 364 ],
      "cpuUsages" : [ 1476, 1476, 1476, 1476, 1162, 1156, 1157, 1127, 1092, 1093, 1116, 1123 ],
      "vmemKbytes" : [ 176827, 530481, 884136, 1237790, 1420639, 1420691, 1420692, 1420691, 1420692, 1420692, 1420691, 1420692 ],
      "physMemKbytes" : [ 38211, 114635, 191059, 267482, 307826, 309555, 311274, 312396, 309356, 305514, 301727, 298067 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72234,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14930,
        "virtualMemoryUsage" : 1454788608,
        "physicalMemoryUsage" : 303349760,
        "heapUsage" : 740229120
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72234,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059058,
    "taskID" : "task_1369942127770_1205_m_000010",
    "taskType" : "MAP",
    "finishTime" : 1371222079719,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2115.smile.com" ]
      },
      "hostName" : "/default-rack/a2115.smile.com",
      "startTime" : 1371222061175,
      "result" : "SUCCESS",
      "finishTime" : 1371222079719,
      "attemptID" : "attempt_1369942127770_1205_m_000010_0",
      "clockSplits" : [ 3057, 3058, 3057, 1958, 885, 884, 885, 835, 833, 832, 1597, 655 ],
      "cpuUsages" : [ 1654, 1654, 1654, 1336, 1024, 1025, 1024, 1025, 1025, 1025, 1040, 1124 ],
      "vmemKbytes" : [ 201676, 605031, 1008386, 1360094, 1409336, 1409383, 1409432, 1409459, 1409460, 1409460, 1409459, 1409460 ],
      "physMemKbytes" : [ 44104, 132313, 220523, 297458, 308371, 308546, 308722, 308946, 309231, 309516, 309798, 309880 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14610,
        "virtualMemoryUsage" : 1443287040,
        "physicalMemoryUsage" : 317300736,
        "heapUsage" : 740556800
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059058,
    "taskID" : "task_1369942127770_1205_m_000011",
    "taskType" : "MAP",
    "finishTime" : 1371222080741,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2115.smile.com" ]
      },
      "hostName" : "/default-rack/a2115.smile.com",
      "startTime" : 1371222061174,
      "result" : "SUCCESS",
      "finishTime" : 1371222080741,
      "attemptID" : "attempt_1369942127770_1205_m_000011_0",
      "clockSplits" : [ 2091, 2091, 2092, 2091, 2092, 1144, 1117, 1175, 1617, 3521, 265, 266 ],
      "cpuUsages" : [ 1356, 1356, 1356, 1356, 1356, 1094, 1087, 1096, 1172, 1157, 1062, 1062 ],
      "vmemKbytes" : [ 141112, 423336, 705560, 987783, 1270008, 1419228, 1419348, 1419347, 1419348, 1419348, 1419347, 1419348 ],
      "physMemKbytes" : [ 30717, 92154, 153590, 215025, 276462, 309084, 309404, 309697, 309866, 309922, 307623, 303864 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14510,
        "virtualMemoryUsage" : 1453412352,
        "physicalMemoryUsage" : 309231616,
        "heapUsage" : 723582976
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72236,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222059058,
    "taskID" : "task_1369942127770_1205_m_000012",
    "taskType" : "MAP",
    "finishTime" : 1371222080631,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2115.smile.com" ]
      },
      "hostName" : "/default-rack/a2115.smile.com",
      "startTime" : 1371222061173,
      "result" : "SUCCESS",
      "finishTime" : 1371222080631,
      "attemptID" : "attempt_1369942127770_1205_m_000012_0",
      "clockSplits" : [ 2152, 2152, 2152, 2152, 2152, 1134, 1107, 1143, 1402, 1403, 2199, 298 ],
      "cpuUsages" : [ 1365, 1365, 1365, 1365, 1366, 1024, 1015, 1026, 1101, 1102, 1136, 1140 ],
      "vmemKbytes" : [ 140246, 420739, 701232, 981725, 1262218, 1409325, 1409412, 1409411, 1409412, 1409412, 1409411, 1409412 ],
      "physMemKbytes" : [ 29429, 88287, 147145, 206002, 264861, 296705, 298774, 300812, 301651, 301721, 300159, 296175 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14370,
        "virtualMemoryUsage" : 1443237888,
        "physicalMemoryUsage" : 301232128,
        "heapUsage" : 745209856
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059059,
    "taskID" : "task_1369942127770_1205_m_000013",
    "taskType" : "MAP",
    "finishTime" : 1371222080047,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2115.smile.com" ]
      },
      "hostName" : "/default-rack/a2115.smile.com",
      "startTime" : 1371222061177,
      "result" : "SUCCESS",
      "finishTime" : 1371222080047,
      "attemptID" : "attempt_1369942127770_1205_m_000013_0",
      "clockSplits" : [ 2415, 2415, 2416, 2415, 1669, 929, 929, 927, 906, 905, 906, 2031 ],
      "cpuUsages" : [ 1582, 1583, 1583, 1583, 1323, 1064, 1064, 1052, 899, 899, 899, 1289 ],
      "vmemKbytes" : [ 156678, 470034, 783390, 1096746, 1370593, 1409423, 1409424, 1409423, 1409424, 1409424, 1409423, 1409424 ],
      "physMemKbytes" : [ 33111, 99335, 165559, 231782, 289734, 298476, 299089, 299699, 299882, 299738, 299593, 299448 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14820,
        "virtualMemoryUsage" : 1443250176,
        "physicalMemoryUsage" : 306561024,
        "heapUsage" : 740622336
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059060,
    "taskID" : "task_1369942127770_1205_m_000014",
    "taskType" : "MAP",
    "finishTime" : 1371222080403,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2116.smile.com" ]
      },
      "hostName" : "/default-rack/a2116.smile.com",
      "startTime" : 1371222061182,
      "result" : "SUCCESS",
      "finishTime" : 1371222080403,
      "attemptID" : "attempt_1369942127770_1205_m_000014_0",
      "clockSplits" : [ 2246, 2247, 2247, 2246, 1771, 975, 975, 1058, 1672, 3290, 244, 244 ],
      "cpuUsages" : [ 1482, 1483, 1483, 1483, 1333, 1082, 1081, 1076, 1036, 1050, 1095, 1096 ],
      "vmemKbytes" : [ 153262, 459788, 766314, 1072839, 1357907, 1417931, 1417932, 1417931, 1417932, 1417932, 1417931, 1417932 ],
      "physMemKbytes" : [ 32426, 97279, 162133, 226986, 287711, 305142, 311027, 316866, 318854, 318343, 317108, 315553 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14780,
        "virtualMemoryUsage" : 1451962368,
        "physicalMemoryUsage" : 322330624,
        "heapUsage" : 668336128
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72236,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222059060,
    "taskID" : "task_1369942127770_1205_m_000015",
    "taskType" : "MAP",
    "finishTime" : 1371222080385,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2116.smile.com" ]
      },
      "hostName" : "/default-rack/a2116.smile.com",
      "startTime" : 1371222061182,
      "result" : "SUCCESS",
      "finishTime" : 1371222080385,
      "attemptID" : "attempt_1369942127770_1205_m_000015_0",
      "clockSplits" : [ 2112, 2113, 2112, 2113, 2112, 1311, 1311, 1686, 1973, 1973, 191, 191 ],
      "cpuUsages" : [ 1520, 1520, 1521, 1520, 1520, 1067, 1068, 1204, 1311, 863, 843, 843 ],
      "vmemKbytes" : [ 142431, 427293, 712156, 997018, 1281881, 1423875, 1423876, 1423875, 1423876, 1423876, 1423875, 1423876 ],
      "physMemKbytes" : [ 31512, 94536, 157561, 220586, 283611, 315540, 316563, 317494, 317993, 317677, 316454, 315229 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14800,
        "virtualMemoryUsage" : 1458049024,
        "physicalMemoryUsage" : 322166784,
        "heapUsage" : 663617536
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059060,
    "taskID" : "task_1369942127770_1205_m_000016",
    "taskType" : "MAP",
    "finishTime" : 1371222080314,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2116.smile.com" ]
      },
      "hostName" : "/default-rack/a2116.smile.com",
      "startTime" : 1371222061183,
      "result" : "SUCCESS",
      "finishTime" : 1371222080314,
      "attemptID" : "attempt_1369942127770_1205_m_000016_0",
      "clockSplits" : [ 2331, 2332, 2332, 2332, 1845, 1142, 1143, 1145, 1148, 1148, 1817, 406 ],
      "cpuUsages" : [ 1376, 1377, 1377, 1377, 1290, 1165, 1165, 1121, 1081, 1082, 1000, 979 ],
      "vmemKbytes" : [ 154918, 464754, 774590, 1084425, 1368248, 1422215, 1422216, 1422215, 1422216, 1422216, 1422215, 1422216 ],
      "physMemKbytes" : [ 31161, 93486, 155811, 218135, 275753, 291774, 298030, 303393, 303746, 303320, 303009, 302957 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14390,
        "virtualMemoryUsage" : 1456349184,
        "physicalMemoryUsage" : 310206464,
        "heapUsage" : 663683072
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059061,
    "taskID" : "task_1369942127770_1205_m_000017",
    "taskType" : "MAP",
    "finishTime" : 1371222079863,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2116.smile.com" ]
      },
      "hostName" : "/default-rack/a2116.smile.com",
      "startTime" : 1371222061183,
      "result" : "SUCCESS",
      "finishTime" : 1371222079863,
      "attemptID" : "attempt_1369942127770_1205_m_000017_0",
      "clockSplits" : [ 2120, 2120, 2121, 2120, 2120, 1291, 1115, 1115, 847, 837, 837, 2031 ],
      "cpuUsages" : [ 1314, 1314, 1314, 1314, 1314, 1108, 1065, 1065, 889, 882, 882, 1289 ],
      "vmemKbytes" : [ 136175, 408526, 680878, 953229, 1225581, 1405242, 1409412, 1409411, 1409412, 1409412, 1409411, 1409412 ],
      "physMemKbytes" : [ 25452, 76356, 127261, 178166, 229071, 262663, 263476, 263509, 263661, 263953, 264244, 264609 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13750,
        "virtualMemoryUsage" : 1443237888,
        "physicalMemoryUsage" : 271626240,
        "heapUsage" : 592183296
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72236,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222059061,
    "taskID" : "task_1369942127770_1205_m_000018",
    "taskType" : "MAP",
    "finishTime" : 1371222080024,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2116.smile.com" ]
      },
      "hostName" : "/default-rack/a2116.smile.com",
      "startTime" : 1371222061180,
      "result" : "SUCCESS",
      "finishTime" : 1371222080024,
      "attemptID" : "attempt_1369942127770_1205_m_000018_0",
      "clockSplits" : [ 2373, 2374, 2374, 2373, 1909, 925, 926, 925, 890, 882, 882, 2005 ],
      "cpuUsages" : [ 1613, 1614, 1613, 1614, 1458, 1128, 1127, 1128, 917, 876, 876, 1256 ],
      "vmemKbytes" : [ 151697, 455092, 758487, 1061882, 1349627, 1419523, 1419524, 1419523, 1419524, 1419524, 1419523, 1419524 ],
      "physMemKbytes" : [ 30652, 91958, 153263, 214568, 272912, 290016, 293889, 297762, 300019, 299336, 298590, 297860 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 15220,
        "virtualMemoryUsage" : 1453592576,
        "physicalMemoryUsage" : 304734208,
        "heapUsage" : 665190400
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059061,
    "taskID" : "task_1369942127770_1205_m_000019",
    "taskType" : "MAP",
    "finishTime" : 1371222081077,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2116.smile.com" ]
      },
      "hostName" : "/default-rack/a2116.smile.com",
      "startTime" : 1371222061181,
      "result" : "SUCCESS",
      "finishTime" : 1371222081077,
      "attemptID" : "attempt_1369942127770_1205_m_000019_0",
      "clockSplits" : [ 2353, 2354, 2353, 2354, 1929, 1071, 1072, 1395, 2209, 2348, 224, 224 ],
      "cpuUsages" : [ 1428, 1429, 1429, 1428, 1376, 1271, 1270, 1203, 1037, 908, 890, 891 ],
      "vmemKbytes" : [ 152162, 456488, 760814, 1065139, 1352750, 1420763, 1420764, 1420763, 1420764, 1420764, 1420763, 1420764 ],
      "physMemKbytes" : [ 32961, 98884, 164808, 230731, 293309, 311938, 316956, 321727, 322219, 321123, 319983, 318842 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14560,
        "virtualMemoryUsage" : 1454862336,
        "physicalMemoryUsage" : 325910528,
        "heapUsage" : 663552000
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059062,
    "taskID" : "task_1369942127770_1205_m_000020",
    "taskType" : "MAP",
    "finishTime" : 1371222080321,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2116.smile.com" ]
      },
      "hostName" : "/default-rack/a2116.smile.com",
      "startTime" : 1371222061184,
      "result" : "SUCCESS",
      "finishTime" : 1371222080321,
      "attemptID" : "attempt_1369942127770_1205_m_000020_0",
      "clockSplits" : [ 2427, 2428, 2428, 2428, 2102, 1517, 1285, 748, 748, 747, 1610, 664 ],
      "cpuUsages" : [ 1443, 1443, 1443, 1443, 1389, 1289, 1200, 994, 995, 995, 1024, 1202 ],
      "vmemKbytes" : [ 153064, 459194, 765324, 1071453, 1357999, 1421147, 1421148, 1421147, 1421148, 1421148, 1421147, 1421148 ],
      "physMemKbytes" : [ 29737, 89213, 148688, 208163, 263841, 276188, 276277, 276205, 276076, 275947, 275853, 277999 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14860,
        "virtualMemoryUsage" : 1455255552,
        "physicalMemoryUsage" : 286453760,
        "heapUsage" : 616431616
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72236,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222059062,
    "taskID" : "task_1369942127770_1205_m_000021",
    "taskType" : "MAP",
    "finishTime" : 1371222081794,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2116.smile.com" ]
      },
      "hostName" : "/default-rack/a2116.smile.com",
      "startTime" : 1371222061178,
      "result" : "SUCCESS",
      "finishTime" : 1371222081794,
      "attemptID" : "attempt_1369942127770_1205_m_000021_0",
      "clockSplits" : [ 2597, 2597, 2597, 2597, 1562, 1164, 1163, 5456, 198, 197, 197, 198 ],
      "cpuUsages" : [ 1446, 1447, 1447, 1447, 1261, 1190, 1189, 983, 897, 898, 897, 898 ],
      "vmemKbytes" : [ 164696, 494089, 823482, 1152875, 1396412, 1409139, 1409140, 1409139, 1409140, 1409140, 1409139, 1409140 ],
      "physMemKbytes" : [ 32676, 98028, 163380, 228731, 280650, 296453, 310265, 319456, 319884, 318033, 316181, 314330 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14060,
        "virtualMemoryUsage" : 1442959360,
        "physicalMemoryUsage" : 320843776,
        "heapUsage" : 656801792
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059062,
    "taskID" : "task_1369942127770_1205_m_000022",
    "taskType" : "MAP",
    "finishTime" : 1371222080729,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2117.smile.com" ]
      },
      "hostName" : "/default-rack/a2117.smile.com",
      "startTime" : 1371222061185,
      "result" : "SUCCESS",
      "finishTime" : 1371222080729,
      "attemptID" : "attempt_1369942127770_1205_m_000022_0",
      "clockSplits" : [ 2144, 2145, 2144, 2145, 2144, 1183, 1111, 1280, 2538, 2355, 174, 175 ],
      "cpuUsages" : [ 1386, 1387, 1386, 1387, 1386, 1209, 1196, 1179, 1059, 978, 968, 969 ],
      "vmemKbytes" : [ 140664, 421992, 703320, 984647, 1265976, 1425650, 1426340, 1426339, 1426340, 1426340, 1426339, 1426340 ],
      "physMemKbytes" : [ 29534, 88604, 147673, 206742, 265812, 300766, 304205, 307483, 308781, 307931, 305761, 303579 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14490,
        "virtualMemoryUsage" : 1460572160,
        "physicalMemoryUsage" : 309747712,
        "heapUsage" : 742064128
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059063,
    "taskID" : "task_1369942127770_1205_m_000023",
    "taskType" : "MAP",
    "finishTime" : 1371222080052,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2117.smile.com" ]
      },
      "hostName" : "/default-rack/a2117.smile.com",
      "startTime" : 1371222061186,
      "result" : "SUCCESS",
      "finishTime" : 1371222080052,
      "attemptID" : "attempt_1369942127770_1205_m_000023_0",
      "clockSplits" : [ 1873, 1874, 1874, 1874, 1874, 1622, 923, 924, 924, 1161, 1185, 2750 ],
      "cpuUsages" : [ 1302, 1303, 1302, 1303, 1303, 1229, 1028, 1027, 1027, 755, 727, 1274 ],
      "vmemKbytes" : [ 124166, 372498, 620831, 869164, 1117497, 1357087, 1424104, 1424103, 1424104, 1424104, 1424103, 1424104 ],
      "physMemKbytes" : [ 26868, 80607, 134345, 188083, 241822, 293671, 308210, 308261, 308313, 307095, 304076, 301141 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13580,
        "virtualMemoryUsage" : 1458282496,
        "physicalMemoryUsage" : 307507200,
        "heapUsage" : 736362496
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72236,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222059063,
    "taskID" : "task_1369942127770_1205_m_000024",
    "taskType" : "MAP",
    "finishTime" : 1371222080302,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2117.smile.com" ]
      },
      "hostName" : "/default-rack/a2117.smile.com",
      "startTime" : 1371222061188,
      "result" : "SUCCESS",
      "finishTime" : 1371222080302,
      "attemptID" : "attempt_1369942127770_1205_m_000024_0",
      "clockSplits" : [ 2106, 2107, 2107, 2106, 2107, 1429, 1184, 1182, 1159, 1159, 1920, 542 ],
      "cpuUsages" : [ 1331, 1331, 1332, 1331, 1331, 1174, 1116, 1098, 957, 957, 1087, 1445 ],
      "vmemKbytes" : [ 134890, 404670, 674451, 944231, 1214012, 1410949, 1420432, 1420431, 1420432, 1420432, 1420431, 1420432 ],
      "physMemKbytes" : [ 28316, 84948, 141580, 198211, 254844, 296319, 298789, 299283, 299498, 299534, 299556, 299285 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14490,
        "virtualMemoryUsage" : 1454522368,
        "physicalMemoryUsage" : 306270208,
        "heapUsage" : 742981632
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059063,
    "taskID" : "task_1369942127770_1205_m_000025",
    "taskType" : "MAP",
    "finishTime" : 1371222080632,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2117.smile.com" ]
      },
      "hostName" : "/default-rack/a2117.smile.com",
      "startTime" : 1371222061188,
      "result" : "SUCCESS",
      "finishTime" : 1371222080632,
      "attemptID" : "attempt_1369942127770_1205_m_000025_0",
      "clockSplits" : [ 1860, 1860, 1860, 1861, 1860, 1610, 1421, 3187, 3528, 128, 128, 129 ],
      "cpuUsages" : [ 1300, 1300, 1300, 1300, 1301, 1224, 1167, 1370, 1043, 928, 928, 929 ],
      "vmemKbytes" : [ 130800, 392403, 654005, 915607, 1177210, 1396290, 1420456, 1420455, 1420456, 1420456, 1420455, 1420456 ],
      "physMemKbytes" : [ 29090, 87270, 145451, 203632, 261813, 309693, 310356, 306542, 311703, 310079, 308271, 306464 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14090,
        "virtualMemoryUsage" : 1454546944,
        "physicalMemoryUsage" : 312893440,
        "heapUsage" : 737214464
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059063,
    "taskID" : "task_1369942127770_1205_m_000026",
    "taskType" : "MAP",
    "finishTime" : 1371222080672,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2117.smile.com" ]
      },
      "hostName" : "/default-rack/a2117.smile.com",
      "startTime" : 1371222061191,
      "result" : "SUCCESS",
      "finishTime" : 1371222080672,
      "attemptID" : "attempt_1369942127770_1205_m_000026_0",
      "clockSplits" : [ 2007, 2007, 2008, 2007, 2007, 1238, 1132, 1218, 1845, 3515, 225, 225 ],
      "cpuUsages" : [ 1331, 1332, 1332, 1332, 1332, 1209, 1192, 1188, 1165, 1134, 1096, 1097 ],
      "vmemKbytes" : [ 137621, 412863, 688106, 963348, 1238591, 1407402, 1409404, 1409403, 1409404, 1409404, 1409403, 1409404 ],
      "physMemKbytes" : [ 30191, 90575, 150959, 211342, 271726, 307874, 306038, 303761, 302896, 302808, 301704, 300384 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14740,
        "virtualMemoryUsage" : 1443229696,
        "physicalMemoryUsage" : 306917376,
        "heapUsage" : 740360192
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72236,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222059064,
    "taskID" : "task_1369942127770_1205_m_000027",
    "taskType" : "MAP",
    "finishTime" : 1371222081541,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2117.smile.com" ]
      },
      "hostName" : "/default-rack/a2117.smile.com",
      "startTime" : 1371222061193,
      "result" : "SUCCESS",
      "finishTime" : 1371222081541,
      "attemptID" : "attempt_1369942127770_1205_m_000027_0",
      "clockSplits" : [ 1920, 1921, 1921, 1920, 1921, 1430, 1280, 3682, 1168, 780, 780, 1620 ],
      "cpuUsages" : [ 1362, 1362, 1362, 1362, 1363, 1239, 1201, 1547, 995, 930, 929, 1238 ],
      "vmemKbytes" : [ 137700, 413102, 688504, 963905, 1239308, 1433933, 1441480, 1441479, 1441480, 1441480, 1441479, 1441480 ],
      "physMemKbytes" : [ 29565, 88698, 147830, 206962, 266095, 308482, 312089, 314493, 316975, 315186, 313383, 311581 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14890,
        "virtualMemoryUsage" : 1476075520,
        "physicalMemoryUsage" : 318078976,
        "heapUsage" : 740425728
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059064,
    "taskID" : "task_1369942127770_1205_m_000028",
    "taskType" : "MAP",
    "finishTime" : 1371222080630,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2117.smile.com" ]
      },
      "hostName" : "/default-rack/a2117.smile.com",
      "startTime" : 1371222061190,
      "result" : "SUCCESS",
      "finishTime" : 1371222080630,
      "attemptID" : "attempt_1369942127770_1205_m_000028_0",
      "clockSplits" : [ 2031, 2031, 2032, 2031, 2032, 1270, 1145, 1198, 1543, 3690, 212, 213 ],
      "cpuUsages" : [ 1318, 1318, 1319, 1318, 1318, 1074, 1032, 1028, 997, 1001, 1033, 1034 ],
      "vmemKbytes" : [ 138260, 414780, 691301, 967821, 1244342, 1419237, 1422052, 1422051, 1422052, 1422052, 1422051, 1422052 ],
      "physMemKbytes" : [ 29586, 88758, 147930, 207102, 266275, 304866, 308604, 311744, 312930, 312923, 312015, 310530 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13790,
        "virtualMemoryUsage" : 1456181248,
        "physicalMemoryUsage" : 317222912,
        "heapUsage" : 744620032
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059064,
    "taskID" : "task_1369942127770_1205_m_000029",
    "taskType" : "MAP",
    "finishTime" : 1371222080196,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2117.smile.com" ]
      },
      "hostName" : "/default-rack/a2117.smile.com",
      "startTime" : 1371222061192,
      "result" : "SUCCESS",
      "finishTime" : 1371222080196,
      "attemptID" : "attempt_1369942127770_1205_m_000029_0",
      "clockSplits" : [ 2041, 2041, 2042, 2041, 2041, 1432, 1199, 1193, 1146, 1147, 2130, 545 ],
      "cpuUsages" : [ 1372, 1372, 1372, 1373, 1372, 1165, 1087, 1080, 1032, 1033, 1054, 1088 ],
      "vmemKbytes" : [ 135224, 405672, 676121, 946570, 1217019, 1416578, 1426876, 1426875, 1426876, 1426876, 1426875, 1426876 ],
      "physMemKbytes" : [ 28101, 84303, 140506, 196709, 252912, 295457, 301536, 305584, 305557, 302915, 300478, 299963 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14400,
        "virtualMemoryUsage" : 1461121024,
        "physicalMemoryUsage" : 307138560,
        "heapUsage" : 740556800
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72236,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222059065,
    "taskID" : "task_1369942127770_1205_m_000030",
    "taskType" : "MAP",
    "finishTime" : 1371222095399,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2118.smile.com" ]
      },
      "hostName" : "/default-rack/a2118.smile.com",
      "startTime" : 1371222077057,
      "result" : "SUCCESS",
      "finishTime" : 1371222095399,
      "attemptID" : "attempt_1369942127770_1205_m_000030_0",
      "clockSplits" : [ 2140, 2140, 2140, 2140, 1862, 1004, 1003, 984, 822, 822, 822, 2455 ],
      "cpuUsages" : [ 1207, 1207, 1207, 1207, 1120, 851, 851, 857, 906, 905, 906, 1366 ],
      "vmemKbytes" : [ 149343, 448029, 746716, 1045403, 1335142, 1420323, 1420324, 1420323, 1420324, 1420324, 1420323, 1420324 ],
      "physMemKbytes" : [ 31929, 95787, 159645, 223502, 285438, 303417, 303091, 302749, 300865, 297952, 295038, 292244 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 12590,
        "virtualMemoryUsage" : 1454411776,
        "physicalMemoryUsage" : 298463232,
        "heapUsage" : 728760320
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059065,
    "taskID" : "task_1369942127770_1205_m_000031",
    "taskType" : "MAP",
    "finishTime" : 1371222101752,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2118.smile.com" ]
      },
      "hostName" : "/default-rack/a2118.smile.com",
      "startTime" : 1371222078069,
      "result" : "SUCCESS",
      "finishTime" : 1371222101752,
      "attemptID" : "attempt_1369942127770_1205_m_000031_0",
      "clockSplits" : [ 2274, 2274, 2274, 2274, 3454, 978, 978, 1034, 1463, 1462, 2573, 529 ],
      "cpuUsages" : [ 1334, 1334, 1334, 1335, 1214, 1033, 1033, 1023, 944, 943, 1021, 1022 ],
      "vmemKbytes" : [ 179277, 537833, 896388, 1254943, 1440327, 1440379, 1440380, 1440379, 1440380, 1440380, 1440379, 1440380 ],
      "physMemKbytes" : [ 38140, 114421, 190703, 266984, 306533, 307559, 308886, 310183, 308915, 305992, 304523, 304521 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13810,
        "virtualMemoryUsage" : 1474949120,
        "physicalMemoryUsage" : 315183104,
        "heapUsage" : 728956928
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059065,
    "taskID" : "task_1369942127770_1205_m_000032",
    "taskType" : "MAP",
    "finishTime" : 1371222102468,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2118.smile.com" ]
      },
      "hostName" : "/default-rack/a2118.smile.com",
      "startTime" : 1371222080073,
      "result" : "SUCCESS",
      "finishTime" : 1371222102468,
      "attemptID" : "attempt_1369942127770_1205_m_000032_0",
      "clockSplits" : [ 2030, 2030, 2031, 2030, 1666, 982, 982, 1072, 1743, 1650, 1476, 4204 ],
      "cpuUsages" : [ 1109, 1109, 1109, 1110, 1120, 1139, 1140, 1119, 972, 937, 873, 1313 ],
      "vmemKbytes" : [ 153120, 459362, 765604, 1071845, 1359504, 1424519, 1424520, 1424519, 1424520, 1424520, 1424519, 1424520 ],
      "physMemKbytes" : [ 32822, 98466, 164110, 229754, 291374, 304780, 304107, 303437, 303182, 302954, 299856, 296038 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13420,
        "virtualMemoryUsage" : 1458708480,
        "physicalMemoryUsage" : 307167232,
        "heapUsage" : 728825856
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72236,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222059065,
    "taskID" : "task_1369942127770_1205_m_000033",
    "taskType" : "MAP",
    "finishTime" : 1371222101998,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2116.smile.com" ]
      },
      "hostName" : "/default-rack/a2116.smile.com",
      "startTime" : 1371222080074,
      "result" : "SUCCESS",
      "finishTime" : 1371222101998,
      "attemptID" : "attempt_1369942127770_1205_m_000033_0",
      "clockSplits" : [ 2253, 2254, 2254, 2254, 1075, 1055, 1055, 1607, 1658, 1287, 1210, 3957 ],
      "cpuUsages" : [ 1286, 1287, 1287, 1287, 1060, 1056, 1056, 1356, 1385, 1130, 1076, 1364 ],
      "vmemKbytes" : [ 176727, 530182, 883638, 1237093, 1419839, 1419891, 1419892, 1419891, 1419892, 1419892, 1419891, 1419892 ],
      "physMemKbytes" : [ 34968, 104906, 174844, 244781, 281486, 282625, 283754, 284431, 284483, 285884, 289832, 293683 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14630,
        "virtualMemoryUsage" : 1453969408,
        "physicalMemoryUsage" : 301670400,
        "heapUsage" : 677380096
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059066,
    "taskID" : "task_1369942127770_1205_m_000034",
    "taskType" : "MAP",
    "finishTime" : 1371222102416,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2115.smile.com" ]
      },
      "hostName" : "/default-rack/a2115.smile.com",
      "startTime" : 1371222080074,
      "result" : "SUCCESS",
      "finishTime" : 1371222102416,
      "attemptID" : "attempt_1369942127770_1205_m_000034_0",
      "clockSplits" : [ 2608, 2609, 2608, 2609, 1753, 770, 771, 770, 856, 994, 994, 3824 ],
      "cpuUsages" : [ 1296, 1297, 1296, 1297, 1205, 1099, 1098, 1099, 1020, 889, 890, 1044 ],
      "vmemKbytes" : [ 158696, 476089, 793483, 1110876, 1393919, 1439299, 1439300, 1439299, 1439300, 1439300, 1439299, 1439300 ],
      "physMemKbytes" : [ 34127, 102381, 170635, 238889, 299969, 311412, 313378, 315343, 317033, 315889, 314000, 312155 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13820,
        "virtualMemoryUsage" : 1473843200,
        "physicalMemoryUsage" : 319066112,
        "heapUsage" : 740556800
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059066,
    "taskID" : "task_1369942127770_1205_m_000035",
    "taskType" : "MAP",
    "finishTime" : 1371222102415,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2118.smile.com" ]
      },
      "hostName" : "/default-rack/a2118.smile.com",
      "startTime" : 1371222081100,
      "result" : "SUCCESS",
      "finishTime" : 1371222102415,
      "attemptID" : "attempt_1369942127770_1205_m_000035_0",
      "clockSplits" : [ 4636, 4637, 1169, 1033, 1033, 670, 655, 655, 655, 3321, 143, 143 ],
      "cpuUsages" : [ 1717, 1718, 1013, 985, 986, 885, 881, 881, 881, 908, 1012, 1013 ],
      "vmemKbytes" : [ 348902, 1046707, 1421400, 1421895, 1421896, 1421895, 1421896, 1421895, 1421896, 1421896, 1421895, 1421896 ],
      "physMemKbytes" : [ 71074, 213222, 292772, 299823, 306780, 310573, 310648, 310717, 310786, 310854, 310865, 310851 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13200,
        "virtualMemoryUsage" : 1456021504,
        "physicalMemoryUsage" : 318390272,
        "heapUsage" : 731447296
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72236,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222059066,
    "taskID" : "task_1369942127770_1205_m_000036",
    "taskType" : "MAP",
    "finishTime" : 1371222125169,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2118.smile.com" ]
      },
      "hostName" : "/default-rack/a2118.smile.com",
      "startTime" : 1371222081101,
      "result" : "SUCCESS",
      "finishTime" : 1371222125169,
      "attemptID" : "attempt_1369942127770_1205_m_000036_0",
      "clockSplits" : [ 6230, 3745, 1243, 1242, 1250, 1249, 1335, 1441, 1332, 1024, 1024, 22949 ],
      "cpuUsages" : [ 2117, 1566, 1010, 1011, 961, 960, 1002, 1055, 988, 801, 801, 1438 ],
      "vmemKbytes" : [ 474617, 1306098, 1425656, 1425655, 1425656, 1425655, 1425656, 1425655, 1425656, 1425656, 1425655, 1425656 ],
      "physMemKbytes" : [ 88814, 246089, 280314, 293875, 300976, 301213, 301436, 301555, 301570, 299938, 297659, 295692 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13710,
        "virtualMemoryUsage" : 1459871744,
        "physicalMemoryUsage" : 303833088,
        "heapUsage" : 727973888
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059067,
    "taskID" : "task_1369942127770_1205_m_000037",
    "taskType" : "MAP",
    "finishTime" : 1371222103766,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2118.smile.com" ]
      },
      "hostName" : "/default-rack/a2118.smile.com",
      "startTime" : 1371222081107,
      "result" : "SUCCESS",
      "finishTime" : 1371222103766,
      "attemptID" : "attempt_1369942127770_1205_m_000037_0",
      "clockSplits" : [ 5242, 4571, 1409, 1409, 1234, 1232, 1096, 946, 945, 2922, 485, 485 ],
      "cpuUsages" : [ 1863, 1705, 964, 963, 1018, 1018, 961, 896, 896, 892, 877, 877 ],
      "vmemKbytes" : [ 390265, 1158821, 1424332, 1424331, 1424332, 1424331, 1424332, 1424331, 1424332, 1424332, 1424331, 1424332 ],
      "physMemKbytes" : [ 77364, 229859, 288579, 297802, 302713, 303041, 303341, 303445, 303514, 303521, 301077, 297356 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13440,
        "virtualMemoryUsage" : 1458515968,
        "physicalMemoryUsage" : 303296512,
        "heapUsage" : 723582976
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059067,
    "taskID" : "task_1369942127770_1205_m_000038",
    "taskType" : "MAP",
    "finishTime" : 1371222102985,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2115.smile.com" ]
      },
      "hostName" : "/default-rack/a2115.smile.com",
      "startTime" : 1371222081108,
      "result" : "SUCCESS",
      "finishTime" : 1371222102985,
      "attemptID" : "attempt_1369942127770_1205_m_000038_0",
      "clockSplits" : [ 3604, 3605, 2556, 894, 894, 894, 1661, 1683, 801, 800, 800, 3680 ],
      "cpuUsages" : [ 1519, 1519, 1354, 1093, 1093, 1093, 1206, 1172, 767, 766, 766, 1152 ],
      "vmemKbytes" : [ 269653, 808959, 1307904, 1409271, 1409272, 1409271, 1409272, 1409271, 1409272, 1409272, 1409271, 1409272 ],
      "physMemKbytes" : [ 56075, 168226, 272040, 293728, 294479, 295228, 297304, 301666, 302286, 300462, 298637, 296864 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13500,
        "virtualMemoryUsage" : 1443094528,
        "physicalMemoryUsage" : 303415296,
        "heapUsage" : 734986240
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72236,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222059067,
    "taskID" : "task_1369942127770_1205_m_000039",
    "taskType" : "MAP",
    "finishTime" : 1371222102412,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2117.smile.com" ]
      },
      "hostName" : "/default-rack/a2117.smile.com",
      "startTime" : 1371222081122,
      "result" : "SUCCESS",
      "finishTime" : 1371222102412,
      "attemptID" : "attempt_1369942127770_1205_m_000039_0",
      "clockSplits" : [ 2243, 2243, 2243, 2243, 1297, 1281, 1209, 1149, 1148, 1142, 1139, 1139 ],
      "cpuUsages" : [ 1346, 1347, 1347, 1346, 1136, 1131, 1212, 1279, 1278, 996, 906, 906 ],
      "vmemKbytes" : [ 176693, 530079, 883466, 1236853, 1419563, 1419615, 1419616, 1419615, 1419616, 1419616, 1419615, 1419616 ],
      "physMemKbytes" : [ 38075, 114226, 190377, 266528, 305978, 306151, 306399, 307073, 307808, 308344, 308411, 308460 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14520,
        "virtualMemoryUsage" : 1453686784,
        "physicalMemoryUsage" : 315822080,
        "heapUsage" : 732823552
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059068,
    "taskID" : "task_1369942127770_1205_m_000040",
    "taskType" : "MAP",
    "finishTime" : 1371222103554,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2116.smile.com" ]
      },
      "hostName" : "/default-rack/a2116.smile.com",
      "startTime" : 1371222081118,
      "result" : "SUCCESS",
      "finishTime" : 1371222103554,
      "attemptID" : "attempt_1369942127770_1205_m_000040_0",
      "clockSplits" : [ 4372, 4372, 2703, 1678, 1229, 1040, 1041, 902, 737, 738, 738, 2881 ],
      "cpuUsages" : [ 1756, 1756, 1411, 1200, 1199, 1198, 1199, 1027, 823, 823, 823, 1025 ],
      "vmemKbytes" : [ 298429, 895288, 1377502, 1420639, 1420640, 1420639, 1420640, 1420639, 1420640, 1420640, 1420639, 1420640 ],
      "physMemKbytes" : [ 58537, 175611, 270284, 279159, 280291, 283392, 286616, 290044, 294932, 300108, 305284, 310287 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14240,
        "virtualMemoryUsage" : 1454735360,
        "physicalMemoryUsage" : 319156224,
        "heapUsage" : 678690816
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059068,
    "taskID" : "task_1369942127770_1205_m_000041",
    "taskType" : "MAP",
    "finishTime" : 1371222101748,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2115.smile.com" ]
      },
      "hostName" : "/default-rack/a2115.smile.com",
      "startTime" : 1371222081109,
      "result" : "SUCCESS",
      "finishTime" : 1371222101748,
      "attemptID" : "attempt_1369942127770_1205_m_000041_0",
      "clockSplits" : [ 3312, 3313, 3219, 801, 802, 802, 802, 889, 889, 889, 2693, 837 ],
      "cpuUsages" : [ 1508, 1509, 1490, 985, 985, 985, 986, 1108, 1109, 1108, 971, 846 ],
      "vmemKbytes" : [ 242835, 728505, 1213832, 1438767, 1438768, 1438767, 1438768, 1438767, 1438768, 1438768, 1438767, 1438768 ],
      "physMemKbytes" : [ 52179, 156537, 260822, 309253, 309436, 309618, 309801, 308717, 306403, 304088, 302092, 301832 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13690,
        "virtualMemoryUsage" : 1473298432,
        "physicalMemoryUsage" : 309014528,
        "heapUsage" : 723976192
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72236,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222059068,
    "taskID" : "task_1369942127770_1205_m_000042",
    "taskType" : "MAP",
    "finishTime" : 1371222103767,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2115.smile.com" ]
      },
      "hostName" : "/default-rack/a2115.smile.com",
      "startTime" : 1371222081119,
      "result" : "SUCCESS",
      "finishTime" : 1371222103767,
      "attemptID" : "attempt_1369942127770_1205_m_000042_0",
      "clockSplits" : [ 3561, 3561, 3262, 2606, 888, 859, 859, 1003, 1314, 1315, 3006, 196 ],
      "cpuUsages" : [ 1552, 1552, 1459, 1255, 1134, 1133, 1132, 1045, 857, 857, 789, 785 ],
      "vmemKbytes" : [ 267994, 803983, 1313613, 1439867, 1439868, 1439867, 1439868, 1439867, 1439868, 1439868, 1439867, 1439868 ],
      "physMemKbytes" : [ 57155, 171467, 280300, 309448, 311589, 312912, 314235, 315324, 313077, 309747, 307880, 307877 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13740,
        "virtualMemoryUsage" : 1474424832,
        "physicalMemoryUsage" : 318595072,
        "heapUsage" : 723779584
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059068,
    "taskID" : "task_1369942127770_1205_m_000043",
    "taskType" : "MAP",
    "finishTime" : 1371222103881,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2117.smile.com" ]
      },
      "hostName" : "/default-rack/a2117.smile.com",
      "startTime" : 1371222081121,
      "result" : "SUCCESS",
      "finishTime" : 1371222103881,
      "attemptID" : "attempt_1369942127770_1205_m_000043_0",
      "clockSplits" : [ 3716, 3716, 2909, 1143, 1144, 1262, 1372, 1465, 1825, 3631, 197, 197 ],
      "cpuUsages" : [ 1623, 1623, 1461, 1106, 1107, 1215, 1316, 1250, 988, 939, 886, 886 ],
      "vmemKbytes" : [ 262336, 787009, 1285817, 1409271, 1409272, 1409271, 1409272, 1409271, 1409272, 1409272, 1409271, 1409272 ],
      "physMemKbytes" : [ 54165, 162495, 265672, 294053, 297834, 301158, 301963, 302374, 302514, 302198, 299793, 297016 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14500,
        "virtualMemoryUsage" : 1443094528,
        "physicalMemoryUsage" : 302665728,
        "heapUsage" : 723648512
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059069,
    "taskID" : "task_1369942127770_1205_m_000044",
    "taskType" : "MAP",
    "finishTime" : 1371222102868,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2117.smile.com" ]
      },
      "hostName" : "/default-rack/a2117.smile.com",
      "startTime" : 1371222081121,
      "result" : "SUCCESS",
      "finishTime" : 1371222102868,
      "attemptID" : "attempt_1369942127770_1205_m_000044_0",
      "clockSplits" : [ 3420, 3421, 2683, 1617, 1400, 1158, 1158, 3301, 2885, 154, 154, 155 ],
      "cpuUsages" : [ 1443, 1443, 1405, 1350, 1224, 1085, 1085, 1191, 924, 903, 903, 904 ],
      "vmemKbytes" : [ 271947, 815841, 1314315, 1409403, 1409404, 1409403, 1409404, 1409403, 1409404, 1409404, 1409403, 1409404 ],
      "physMemKbytes" : [ 56476, 169428, 273297, 296495, 300225, 300941, 301104, 302318, 304696, 304473, 304240, 304008 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13940,
        "virtualMemoryUsage" : 1443229696,
        "physicalMemoryUsage" : 311136256,
        "heapUsage" : 740360192
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72236,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222059069,
    "taskID" : "task_1369942127770_1205_m_000045",
    "taskType" : "MAP",
    "finishTime" : 1371222127471,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2115.smile.com" ]
      },
      "hostName" : "/default-rack/a2115.smile.com",
      "startTime" : 1371222081121,
      "result" : "SUCCESS",
      "finishTime" : 1371222127471,
      "attemptID" : "attempt_1369942127770_1205_m_000045_0",
      "clockSplits" : [ 4495, 4496, 1624, 1019, 1020, 1151, 1211, 1978, 28975, 125, 125, 125 ],
      "cpuUsages" : [ 1757, 1757, 1255, 1150, 1150, 1108, 1090, 1085, 1209, 816, 816, 817 ],
      "vmemKbytes" : [ 326114, 978343, 1408153, 1418043, 1418044, 1418043, 1418044, 1418043, 1418044, 1418044, 1418043, 1418044 ],
      "physMemKbytes" : [ 67381, 202144, 292944, 300742, 306585, 311066, 311413, 311477, 311272, 310097, 308858, 307619 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14010,
        "virtualMemoryUsage" : 1452077056,
        "physicalMemoryUsage" : 314368000,
        "heapUsage" : 721223680
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059069,
    "taskID" : "task_1369942127770_1205_m_000046",
    "taskType" : "MAP",
    "finishTime" : 1371222124895,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2116.smile.com" ]
      },
      "hostName" : "/default-rack/a2116.smile.com",
      "startTime" : 1371222082113,
      "result" : "SUCCESS",
      "finishTime" : 1371222124895,
      "attemptID" : "attempt_1369942127770_1205_m_000046_0",
      "clockSplits" : [ 3256, 3257, 3256, 3257, 1592, 889, 889, 867, 781, 782, 781, 23168 ],
      "cpuUsages" : [ 1366, 1366, 1366, 1366, 1331, 1318, 1318, 1230, 906, 907, 906, 1320 ],
      "vmemKbytes" : [ 165057, 495172, 825288, 1155403, 1403989, 1418567, 1418568, 1418567, 1418568, 1418568, 1418567, 1418568 ],
      "physMemKbytes" : [ 30439, 91319, 152199, 213078, 259370, 263787, 265596, 267636, 276563, 288702, 300841, 312600 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14700,
        "virtualMemoryUsage" : 1452613632,
        "physicalMemoryUsage" : 323588096,
        "heapUsage" : 675282944
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059070,
    "taskID" : "task_1369942127770_1205_m_000047",
    "taskType" : "MAP",
    "finishTime" : 1371222102985,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2117.smile.com" ]
      },
      "hostName" : "/default-rack/a2117.smile.com",
      "startTime" : 1371222082114,
      "result" : "SUCCESS",
      "finishTime" : 1371222102985,
      "attemptID" : "attempt_1369942127770_1205_m_000047_0",
      "clockSplits" : [ 4299, 4300, 2006, 1431, 1084, 884, 883, 874, 802, 802, 802, 2699 ],
      "cpuUsages" : [ 1763, 1763, 1335, 1228, 1182, 1155, 1156, 1116, 822, 822, 822, 1126 ],
      "vmemKbytes" : [ 320229, 960687, 1396409, 1409271, 1409272, 1409271, 1409272, 1409271, 1409272, 1409272, 1409271, 1409272 ],
      "physMemKbytes" : [ 63258, 189774, 277972, 287030, 292846, 295605, 298083, 300529, 300299, 298352, 296405, 294521 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14290,
        "virtualMemoryUsage" : 1443094528,
        "physicalMemoryUsage" : 301035520,
        "heapUsage" : 724041728
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72236,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222059070,
    "taskID" : "task_1369942127770_1205_m_000048",
    "taskType" : "MAP",
    "finishTime" : 1371222102897,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2115.smile.com" ]
      },
      "hostName" : "/default-rack/a2115.smile.com",
      "startTime" : 1371222082113,
      "result" : "SUCCESS",
      "finishTime" : 1371222102897,
      "attemptID" : "attempt_1369942127770_1205_m_000048_0",
      "clockSplits" : [ 2803, 2803, 2804, 1930, 902, 901, 902, 3220, 799, 798, 799, 2118 ],
      "cpuUsages" : [ 1423, 1423, 1424, 1233, 1010, 1009, 1009, 1396, 817, 818, 817, 1261 ],
      "vmemKbytes" : [ 199009, 597028, 995047, 1351087, 1409272, 1409271, 1409272, 1409271, 1409272, 1409272, 1409271, 1409272 ],
      "physMemKbytes" : [ 43013, 129039, 215066, 291871, 303240, 301826, 300413, 299727, 299138, 298063, 296988, 296589 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13640,
        "virtualMemoryUsage" : 1443094528,
        "physicalMemoryUsage" : 307924992,
        "heapUsage" : 723648512
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059070,
    "taskID" : "task_1369942127770_1205_m_000049",
    "taskType" : "MAP",
    "finishTime" : 1371222129137,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2116.smile.com" ]
      },
      "hostName" : "/default-rack/a2116.smile.com",
      "startTime" : 1371222082116,
      "result" : "SUCCESS",
      "finishTime" : 1371222129137,
      "attemptID" : "attempt_1369942127770_1205_m_000049_0",
      "clockSplits" : [ 2860, 2861, 2861, 2861, 1894, 1005, 1005, 7694, 5464, 5464, 5464, 7263 ],
      "cpuUsages" : [ 1406, 1406, 1407, 1406, 1267, 1138, 1137, 1367, 736, 735, 736, 1109 ],
      "vmemKbytes" : [ 160162, 480486, 800810, 1121133, 1398054, 1434867, 1434868, 1434867, 1434868, 1434868, 1434867, 1434868 ],
      "physMemKbytes" : [ 30785, 92356, 153927, 215498, 269221, 279523, 283170, 286860, 294606, 304983, 315359, 325403 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13910,
        "virtualMemoryUsage" : 1469304832,
        "physicalMemoryUsage" : 336125952,
        "heapUsage" : 675872768
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059071,
    "taskID" : "task_1369942127770_1205_m_000050",
    "taskType" : "MAP",
    "finishTime" : 1371222103702,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2116.smile.com" ]
      },
      "hostName" : "/default-rack/a2116.smile.com",
      "startTime" : 1371222082115,
      "result" : "SUCCESS",
      "finishTime" : 1371222103702,
      "attemptID" : "attempt_1369942127770_1205_m_000050_0",
      "clockSplits" : [ 3070, 3071, 3071, 3030, 1063, 1064, 1063, 981, 981, 980, 2531, 678 ],
      "cpuUsages" : [ 1472, 1472, 1473, 1465, 1137, 1137, 1136, 1360, 1362, 1361, 920, 815 ],
      "vmemKbytes" : [ 177976, 533928, 889881, 1245762, 1416656, 1416655, 1416656, 1416913, 1417437, 1417960, 1418314, 1418324 ],
      "physMemKbytes" : [ 34783, 104349, 173916, 243470, 282136, 292267, 302399, 307271, 306752, 306233, 305870, 305826 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 15110,
        "virtualMemoryUsage" : 1452363776,
        "physicalMemoryUsage" : 313147392,
        "heapUsage" : 663683072
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72236,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222059071,
    "taskID" : "task_1369942127770_1205_m_000051",
    "taskType" : "MAP",
    "finishTime" : 1371222114762,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2116.smile.com" ]
      },
      "hostName" : "/default-rack/a2116.smile.com",
      "startTime" : 1371222082116,
      "result" : "SUCCESS",
      "finishTime" : 1371222114762,
      "attemptID" : "attempt_1369942127770_1205_m_000051_0",
      "clockSplits" : [ 2776, 2776, 2776, 2776, 2052, 1135, 1135, 15865, 337, 337, 337, 337 ],
      "cpuUsages" : [ 1381, 1382, 1382, 1382, 1259, 1102, 1103, 1505, 806, 806, 806, 806 ],
      "vmemKbytes" : [ 157661, 472983, 788305, 1103627, 1388243, 1437451, 1437452, 1437451, 1437452, 1437452, 1437451, 1437452 ],
      "physMemKbytes" : [ 32208, 96626, 161044, 225461, 284440, 301714, 310271, 317790, 318550, 317867, 317184, 316501 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13720,
        "virtualMemoryUsage" : 1471950848,
        "physicalMemoryUsage" : 323747840,
        "heapUsage" : 657391616
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059071,
    "taskID" : "task_1369942127770_1205_m_000052",
    "taskType" : "MAP",
    "finishTime" : 1371222116084,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2116.smile.com" ]
      },
      "hostName" : "/default-rack/a2116.smile.com",
      "startTime" : 1371222082115,
      "result" : "SUCCESS",
      "finishTime" : 1371222116084,
      "attemptID" : "attempt_1369942127770_1205_m_000052_0",
      "clockSplits" : [ 3007, 3008, 3007, 3008, 1568, 1223, 1222, 15532, 596, 597, 597, 597 ],
      "cpuUsages" : [ 1507, 1507, 1507, 1507, 1238, 1175, 1174, 1321, 776, 776, 776, 776 ],
      "vmemKbytes" : [ 169124, 507373, 845623, 1183872, 1412160, 1418503, 1418504, 1418503, 1418504, 1418504, 1418503, 1418504 ],
      "physMemKbytes" : [ 32359, 97079, 161798, 226517, 275030, 290830, 305697, 313100, 310842, 308599, 306356, 304113 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14040,
        "virtualMemoryUsage" : 1452548096,
        "physicalMemoryUsage" : 310263808,
        "heapUsage" : 656736256
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059071,
    "taskID" : "task_1369942127770_1205_m_000053",
    "taskType" : "MAP",
    "finishTime" : 1371222103694,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2117.smile.com" ]
      },
      "hostName" : "/default-rack/a2117.smile.com",
      "startTime" : 1371222083110,
      "result" : "SUCCESS",
      "finishTime" : 1371222103694,
      "attemptID" : "attempt_1369942127770_1205_m_000053_0",
      "clockSplits" : [ 2723, 2724, 2723, 2724, 1401, 1380, 987, 720, 719, 720, 2336, 1346 ],
      "cpuUsages" : [ 1461, 1462, 1462, 1462, 1176, 1172, 1031, 936, 935, 935, 957, 1051 ],
      "vmemKbytes" : [ 176884, 530654, 884423, 1238192, 1420450, 1420491, 1420492, 1420491, 1420492, 1420492, 1420491, 1420492 ],
      "physMemKbytes" : [ 38706, 116120, 193534, 270947, 310895, 311037, 311316, 312193, 313137, 314081, 315011, 315367 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14100,
        "virtualMemoryUsage" : 1454583808,
        "physicalMemoryUsage" : 322899968,
        "heapUsage" : 742457344
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72236,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222059072,
    "taskID" : "task_1369942127770_1205_m_000054",
    "taskType" : "MAP",
    "finishTime" : 1371222127757,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2117.smile.com" ]
      },
      "hostName" : "/default-rack/a2117.smile.com",
      "startTime" : 1371222083125,
      "result" : "SUCCESS",
      "finishTime" : 1371222127757,
      "attemptID" : "attempt_1369942127770_1205_m_000054_0",
      "clockSplits" : [ 3770, 3770, 3350, 1454, 1455, 1119, 1113, 4738, 5863, 5864, 5864, 5864 ],
      "cpuUsages" : [ 1933, 1934, 1782, 1099, 1099, 1086, 1086, 1152, 789, 790, 790, 790 ],
      "vmemKbytes" : [ 255816, 767448, 1270616, 1440657, 1438670, 1437636, 1437636, 1437623, 1436443, 1434504, 1432565, 1430626 ],
      "physMemKbytes" : [ 53820, 161460, 267313, 302807, 301966, 303414, 307344, 311222, 310193, 305969, 301744, 297520 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14690,
        "virtualMemoryUsage" : 1463967744,
        "physicalMemoryUsage" : 302465024,
        "heapUsage" : 742260736
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059072,
    "taskID" : "task_1369942127770_1205_m_000055",
    "taskType" : "MAP",
    "finishTime" : 1371222103714,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2117.smile.com" ]
      },
      "hostName" : "/default-rack/a2117.smile.com",
      "startTime" : 1371222083112,
      "result" : "SUCCESS",
      "finishTime" : 1371222103714,
      "attemptID" : "attempt_1369942127770_1205_m_000055_0",
      "clockSplits" : [ 2702, 2702, 2702, 2702, 1233, 1210, 1258, 1338, 3529, 268, 268, 268 ],
      "cpuUsages" : [ 1441, 1442, 1442, 1442, 1145, 1140, 1151, 1168, 1147, 810, 811, 811 ],
      "vmemKbytes" : [ 178795, 536385, 893975, 1251564, 1434355, 1431439, 1428693, 1428123, 1428124, 1428124, 1428123, 1428124 ],
      "physMemKbytes" : [ 38002, 114007, 190012, 266017, 303836, 301085, 298665, 299727, 301696, 301927, 300774, 299621 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14200,
        "virtualMemoryUsage" : 1462398976,
        "physicalMemoryUsage" : 306319360,
        "heapUsage" : 740425728
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059072,
    "taskID" : "task_1369942127770_1205_m_000056",
    "taskType" : "MAP",
    "finishTime" : 1371222103849,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2117.smile.com" ]
      },
      "hostName" : "/default-rack/a2117.smile.com",
      "startTime" : 1371222083165,
      "result" : "SUCCESS",
      "finishTime" : 1371222103849,
      "attemptID" : "attempt_1369942127770_1205_m_000056_0",
      "clockSplits" : [ 1942, 1942, 1942, 1943, 1942, 1815, 1471, 1602, 2567, 3041, 158, 159 ],
      "cpuUsages" : [ 1364, 1365, 1365, 1365, 1365, 1347, 1296, 1278, 1135, 891, 864, 865 ],
      "vmemKbytes" : [ 125438, 376316, 627194, 878071, 1128949, 1370541, 1435402, 1432859, 1428772, 1425767, 1425743, 1425744 ],
      "physMemKbytes" : [ 26942, 80826, 134711, 188595, 242480, 294459, 310173, 312011, 309654, 305607, 303001, 300406 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14590,
        "virtualMemoryUsage" : 1459961856,
        "physicalMemoryUsage" : 306216960,
        "heapUsage" : 742785024
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72236,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222059073,
    "taskID" : "task_1369942127770_1205_m_000057",
    "taskType" : "MAP",
    "finishTime" : 1371222103712,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2115.smile.com" ]
      },
      "hostName" : "/default-rack/a2115.smile.com",
      "startTime" : 1371222083127,
      "result" : "SUCCESS",
      "finishTime" : 1371222103712,
      "attemptID" : "attempt_1369942127770_1205_m_000057_0",
      "clockSplits" : [ 2428, 2429, 2428, 2429, 2428, 1847, 997, 998, 1005, 1021, 1022, 1546 ],
      "cpuUsages" : [ 1364, 1364, 1364, 1364, 1364, 1191, 938, 938, 886, 752, 753, 1142 ],
      "vmemKbytes" : [ 128472, 385419, 642365, 899311, 1156258, 1391627, 1433817, 1430184, 1426697, 1425764, 1425763, 1425764 ],
      "physMemKbytes" : [ 27860, 83580, 139301, 195021, 250742, 301700, 310016, 308217, 306366, 303606, 300513, 297518 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13420,
        "virtualMemoryUsage" : 1459982336,
        "physicalMemoryUsage" : 303755264,
        "heapUsage" : 744357888
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059073,
    "taskID" : "task_1369942127770_1205_m_000058",
    "taskType" : "MAP",
    "finishTime" : 1371222122471,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2115.smile.com" ]
      },
      "hostName" : "/default-rack/a2115.smile.com",
      "startTime" : 1371222083128,
      "result" : "SUCCESS",
      "finishTime" : 1371222122471,
      "attemptID" : "attempt_1369942127770_1205_m_000058_0",
      "clockSplits" : [ 2490, 2490, 2491, 2338, 1678, 1612, 1441, 24272, 130, 131, 130, 131 ],
      "cpuUsages" : [ 1390, 1390, 1390, 1352, 1192, 1144, 1016, 1333, 848, 848, 848, 849 ],
      "vmemKbytes" : [ 184824, 554474, 924124, 1287306, 1409448, 1409447, 1409448, 1409447, 1409448, 1409448, 1409447, 1409448 ],
      "physMemKbytes" : [ 39675, 119026, 198377, 276344, 302719, 302849, 301090, 298636, 297427, 296963, 296499, 296036 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13600,
        "virtualMemoryUsage" : 1443274752,
        "physicalMemoryUsage" : 302903296,
        "heapUsage" : 718929920
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059073,
    "taskID" : "task_1369942127770_1205_m_000059",
    "taskType" : "MAP",
    "finishTime" : 1371222124673,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2116.smile.com" ]
      },
      "hostName" : "/default-rack/a2116.smile.com",
      "startTime" : 1371222083120,
      "result" : "SUCCESS",
      "finishTime" : 1371222124673,
      "attemptID" : "attempt_1369942127770_1205_m_000059_0",
      "clockSplits" : [ 2762, 2762, 2763, 2762, 1265, 1242, 1113, 964, 964, 1098, 1562, 22290 ],
      "cpuUsages" : [ 1534, 1534, 1534, 1534, 1104, 1098, 1078, 1053, 1054, 977, 714, 1136 ],
      "vmemKbytes" : [ 178607, 535821, 893035, 1250249, 1432998, 1430391, 1428024, 1427639, 1427640, 1427640, 1427639, 1427640 ],
      "physMemKbytes" : [ 34082, 102247, 170412, 238577, 281383, 297244, 311441, 313974, 314247, 314480, 313649, 312382 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14350,
        "virtualMemoryUsage" : 1461903360,
        "physicalMemoryUsage" : 319496192,
        "heapUsage" : 663027712
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72236,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222059073,
    "taskID" : "task_1369942127770_1205_m_000060",
    "taskType" : "MAP",
    "finishTime" : 1371222116128,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2118.smile.com" ]
      },
      "hostName" : "/default-rack/a2118.smile.com",
      "startTime" : 1371222097139,
      "result" : "SUCCESS",
      "finishTime" : 1371222116128,
      "attemptID" : "attempt_1369942127770_1205_m_000060_0",
      "clockSplits" : [ 1500, 1501, 1500, 1501, 1500, 1501, 1487, 1433, 1426, 1113, 1114, 3407 ],
      "cpuUsages" : [ 1038, 1039, 1038, 1039, 1038, 1039, 1025, 968, 966, 874, 873, 1403 ],
      "vmemKbytes" : [ 105321, 315966, 526611, 737256, 947901, 1158545, 1365245, 1432246, 1429737, 1428540, 1428539, 1428540 ],
      "physMemKbytes" : [ 22420, 67262, 112104, 156945, 201787, 246628, 290619, 304477, 303341, 301354, 298591, 295908 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 12340,
        "virtualMemoryUsage" : 1462824960,
        "physicalMemoryUsage" : 302157824,
        "heapUsage" : 723582976
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059074,
    "taskID" : "task_1369942127770_1205_m_000061",
    "taskType" : "MAP",
    "finishTime" : 1371222116509,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2118.smile.com" ]
      },
      "hostName" : "/default-rack/a2118.smile.com",
      "startTime" : 1371222103155,
      "result" : "SUCCESS",
      "finishTime" : 1371222116509,
      "attemptID" : "attempt_1369942127770_1205_m_000061_0",
      "clockSplits" : [ 1102, 1103, 1103, 1103, 1102, 1103, 1103, 1103, 1105, 1105, 2090, 227 ],
      "cpuUsages" : [ 1023, 1024, 1023, 1024, 1024, 1023, 1024, 998, 914, 913, 1030, 1230 ],
      "vmemKbytes" : [ 91613, 274839, 458066, 641293, 824520, 1007746, 1190973, 1369212, 1423060, 1423060, 1423059, 1423060 ],
      "physMemKbytes" : [ 19978, 59934, 99891, 139848, 179805, 219762, 259719, 298545, 309145, 307525, 306010, 305645 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 12250,
        "virtualMemoryUsage" : 1457213440,
        "physicalMemoryUsage" : 312954880,
        "heapUsage" : 724041728
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059074,
    "taskID" : "task_1369942127770_1205_m_000062",
    "taskType" : "MAP",
    "finishTime" : 1371222120045,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2115.smile.com" ]
      },
      "hostName" : "/default-rack/a2115.smile.com",
      "startTime" : 1371222103156,
      "result" : "SUCCESS",
      "finishTime" : 1371222120045,
      "attemptID" : "attempt_1369942127770_1205_m_000062_0",
      "clockSplits" : [ 1830, 1830, 1831, 1830, 1519, 770, 770, 769, 812, 1065, 1065, 2792 ],
      "cpuUsages" : [ 1251, 1252, 1251, 1252, 1189, 1038, 1037, 1038, 1015, 876, 875, 1306 ],
      "vmemKbytes" : [ 149737, 449211, 748686, 1048161, 1334683, 1409291, 1409292, 1409291, 1409292, 1409292, 1409291, 1409292 ],
      "physMemKbytes" : [ 32142, 96428, 160713, 224998, 286484, 302152, 301689, 301225, 300738, 298783, 295954, 293214 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13380,
        "virtualMemoryUsage" : 1443115008,
        "physicalMemoryUsage" : 299433984,
        "heapUsage" : 723779584
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72236,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222059074,
    "taskID" : "task_1369942127770_1205_m_000063",
    "taskType" : "MAP",
    "finishTime" : 1371222117644,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2117.smile.com" ]
      },
      "hostName" : "/default-rack/a2117.smile.com",
      "startTime" : 1371222103154,
      "result" : "SUCCESS",
      "finishTime" : 1371222117644,
      "attemptID" : "attempt_1369942127770_1205_m_000063_0",
      "clockSplits" : [ 1249, 1250, 1250, 1249, 1250, 1250, 1298, 1478, 4009, 67, 67, 67 ],
      "cpuUsages" : [ 1103, 1103, 1103, 1104, 1103, 1103, 1096, 1068, 1060, 989, 989, 989 ],
      "vmemKbytes" : [ 104541, 313625, 522708, 731791, 940876, 1149959, 1354370, 1419379, 1419380, 1419380, 1419379, 1419380 ],
      "physMemKbytes" : [ 22813, 68439, 114065, 159690, 205317, 250942, 295600, 311369, 313657, 314573, 314492, 314412 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 12810,
        "virtualMemoryUsage" : 1453445120,
        "physicalMemoryUsage" : 321916928,
        "heapUsage" : 728825856
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059075,
    "taskID" : "task_1369942127770_1205_m_000064",
    "taskType" : "MAP",
    "finishTime" : 1371222117257,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2115.smile.com" ]
      },
      "hostName" : "/default-rack/a2115.smile.com",
      "startTime" : 1371222103156,
      "result" : "SUCCESS",
      "finishTime" : 1371222117257,
      "attemptID" : "attempt_1369942127770_1205_m_000064_0",
      "clockSplits" : [ 1141, 1141, 1142, 1141, 1142, 1141, 1142, 1624, 2159, 1884, 219, 220 ],
      "cpuUsages" : [ 1100, 1100, 1101, 1100, 1100, 1101, 1100, 1111, 1123, 880, 877, 877 ],
      "vmemKbytes" : [ 95516, 286549, 477582, 668615, 859648, 1050681, 1241715, 1411172, 1437472, 1437472, 1437471, 1437472 ],
      "physMemKbytes" : [ 20537, 61611, 102686, 143760, 184835, 225910, 266985, 303753, 311949, 312235, 309723, 307212 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 12570,
        "virtualMemoryUsage" : 1471971328,
        "physicalMemoryUsage" : 313298944,
        "heapUsage" : 723582976
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059075,
    "taskID" : "task_1369942127770_1205_m_000065",
    "taskType" : "MAP",
    "finishTime" : 1371222142884,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2116.smile.com" ]
      },
      "hostName" : "/default-rack/a2116.smile.com",
      "startTime" : 1371222103154,
      "result" : "SUCCESS",
      "finishTime" : 1371222142884,
      "attemptID" : "attempt_1369942127770_1205_m_000065_0",
      "clockSplits" : [ 6846, 6847, 6846, 6847, 6846, 1331, 720, 720, 720, 1574, 213, 214 ],
      "cpuUsages" : [ 1068, 1069, 1069, 1068, 1069, 823, 795, 795, 796, 814, 827, 827 ],
      "vmemKbytes" : [ 138171, 414514, 690857, 967200, 1243543, 1407908, 1409284, 1409283, 1409284, 1409284, 1409283, 1409284 ],
      "physMemKbytes" : [ 27592, 82777, 137962, 193147, 248333, 280961, 280755, 280271, 279788, 279136, 277800, 276389 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 11020,
        "virtualMemoryUsage" : 1443106816,
        "physicalMemoryUsage" : 282300416,
        "heapUsage" : 606863360
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72236,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222059075,
    "taskID" : "task_1369942127770_1205_m_000066",
    "taskType" : "MAP",
    "finishTime" : 1371222118279,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2118.smile.com" ]
      },
      "hostName" : "/default-rack/a2118.smile.com",
      "startTime" : 1371222104206,
      "result" : "SUCCESS",
      "finishTime" : 1371222118279,
      "attemptID" : "attempt_1369942127770_1205_m_000066_0",
      "clockSplits" : [ 1309, 1310, 1310, 1310, 1309, 1310, 1093, 740, 741, 741, 2689, 206 ],
      "cpuUsages" : [ 1116, 1117, 1117, 1116, 1117, 1117, 1046, 933, 933, 933, 942, 1113 ],
      "vmemKbytes" : [ 107292, 321876, 536460, 751043, 965628, 1180211, 1379124, 1420075, 1420076, 1420076, 1420075, 1420076 ],
      "physMemKbytes" : [ 23107, 69321, 115536, 161750, 207965, 254180, 296965, 305182, 304437, 303692, 302947, 302581 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 12600,
        "virtualMemoryUsage" : 1454157824,
        "physicalMemoryUsage" : 309813248,
        "heapUsage" : 723517440
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059076,
    "taskID" : "task_1369942127770_1205_m_000067",
    "taskType" : "MAP",
    "finishTime" : 1371222122382,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2117.smile.com" ]
      },
      "hostName" : "/default-rack/a2117.smile.com",
      "startTime" : 1371222104212,
      "result" : "SUCCESS",
      "finishTime" : 1371222122382,
      "attemptID" : "attempt_1369942127770_1205_m_000067_0",
      "clockSplits" : [ 2306, 2306, 2307, 2306, 969, 948, 949, 1054, 1108, 1109, 2584, 218 ],
      "cpuUsages" : [ 1454, 1454, 1455, 1454, 1273, 1269, 1270, 991, 848, 848, 1001, 1033 ],
      "vmemKbytes" : [ 179001, 537003, 895005, 1253007, 1437446, 1437487, 1437488, 1437487, 1437488, 1437488, 1437487, 1437488 ],
      "physMemKbytes" : [ 37869, 113607, 189345, 265083, 304399, 305018, 305630, 306131, 306267, 306373, 305816, 304030 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14350,
        "virtualMemoryUsage" : 1471987712,
        "physicalMemoryUsage" : 310398976,
        "heapUsage" : 723779584
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059076,
    "taskID" : "task_1369942127770_1205_m_000068",
    "taskType" : "MAP",
    "finishTime" : 1371222139789,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2116.smile.com" ]
      },
      "hostName" : "/default-rack/a2116.smile.com",
      "startTime" : 1371222104213,
      "result" : "SUCCESS",
      "finishTime" : 1371222139789,
      "attemptID" : "attempt_1369942127770_1205_m_000068_0",
      "clockSplits" : [ 7214, 7215, 7214, 7215, 722, 608, 609, 609, 608, 2851, 158, 158 ],
      "cpuUsages" : [ 1219, 1220, 1220, 1220, 895, 890, 890, 890, 890, 861, 857, 858 ],
      "vmemKbytes" : [ 178706, 536118, 893530, 1250942, 1435030, 1433622, 1432162, 1430701, 1429241, 1428318, 1428303, 1428304 ],
      "physMemKbytes" : [ 33878, 101634, 169390, 237145, 275703, 283011, 290312, 297612, 304913, 309538, 309654, 309698 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 12160,
        "virtualMemoryUsage" : 1462583296,
        "physicalMemoryUsage" : 317272064,
        "heapUsage" : 661913600
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72236,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222059076,
    "taskID" : "task_1369942127770_1205_m_000069",
    "taskType" : "MAP",
    "finishTime" : 1371222117823,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2115.smile.com" ]
      },
      "hostName" : "/default-rack/a2115.smile.com",
      "startTime" : 1371222104211,
      "result" : "SUCCESS",
      "finishTime" : 1371222117823,
      "attemptID" : "attempt_1369942127770_1205_m_000069_0",
      "clockSplits" : [ 1345, 1346, 1346, 1346, 1345, 1346, 1251, 1080, 1080, 1943, 89, 90 ],
      "cpuUsages" : [ 1283, 1284, 1284, 1284, 1284, 1284, 1262, 1222, 1222, 1068, 886, 887 ],
      "vmemKbytes" : [ 107219, 321657, 536096, 750535, 964974, 1179413, 1380249, 1424691, 1424692, 1424692, 1424691, 1424692 ],
      "physMemKbytes" : [ 23184, 69554, 115924, 162293, 208663, 255032, 298478, 308303, 308574, 308817, 308869, 308884 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14250,
        "virtualMemoryUsage" : 1458884608,
        "physicalMemoryUsage" : 316305408,
        "heapUsage" : 728498176
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059076,
    "taskID" : "task_1369942127770_1205_m_000070",
    "taskType" : "MAP",
    "finishTime" : 1371222120103,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2117.smile.com" ]
      },
      "hostName" : "/default-rack/a2117.smile.com",
      "startTime" : 1371222104209,
      "result" : "SUCCESS",
      "finishTime" : 1371222120103,
      "attemptID" : "attempt_1369942127770_1205_m_000070_0",
      "clockSplits" : [ 2413, 2414, 2414, 2414, 829, 801, 801, 3485, 79, 80, 79, 80 ],
      "cpuUsages" : [ 1351, 1352, 1352, 1351, 1086, 1082, 1081, 1047, 792, 792, 792, 792 ],
      "vmemKbytes" : [ 178427, 535281, 892136, 1248991, 1432584, 1430750, 1428865, 1426991, 1426256, 1426256, 1426255, 1426256 ],
      "physMemKbytes" : [ 37393, 112181, 186969, 261756, 300169, 299652, 299123, 298584, 297156, 295154, 293151, 291149 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 12870,
        "virtualMemoryUsage" : 1460486144,
        "physicalMemoryUsage" : 297111552,
        "heapUsage" : 723582976
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059077,
    "taskID" : "task_1369942127770_1205_m_000071",
    "taskType" : "MAP",
    "finishTime" : 1371222116746,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2118.smile.com" ]
      },
      "hostName" : "/default-rack/a2118.smile.com",
      "startTime" : 1371222104212,
      "result" : "SUCCESS",
      "finishTime" : 1371222116746,
      "attemptID" : "attempt_1369942127770_1205_m_000071_0",
      "clockSplits" : [ 1399, 1400, 1400, 1400, 1400, 1400, 842, 615, 615, 614, 615, 829 ],
      "cpuUsages" : [ 1079, 1080, 1079, 1080, 1079, 1080, 919, 855, 854, 854, 854, 1127 ],
      "vmemKbytes" : [ 114375, 343125, 571876, 800626, 1029377, 1258128, 1429156, 1438747, 1438748, 1438748, 1438747, 1438748 ],
      "physMemKbytes" : [ 24578, 73734, 122891, 172048, 221205, 270361, 306966, 308465, 307878, 307290, 306702, 306207 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 11940,
        "virtualMemoryUsage" : 1473277952,
        "physicalMemoryUsage" : 313466880,
        "heapUsage" : 723648512
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72236,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222059077,
    "taskID" : "task_1369942127770_1205_m_000072",
    "taskType" : "MAP",
    "finishTime" : 1371222119279,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2115.smile.com" ]
      },
      "hostName" : "/default-rack/a2115.smile.com",
      "startTime" : 1371222104210,
      "result" : "SUCCESS",
      "finishTime" : 1371222119279,
      "attemptID" : "attempt_1369942127770_1205_m_000072_0",
      "clockSplits" : [ 1218, 1219, 1219, 1219, 1218, 1219, 1219, 990, 787, 787, 786, 3182 ],
      "cpuUsages" : [ 1182, 1182, 1182, 1182, 1182, 1182, 1182, 1067, 964, 965, 965, 1145 ],
      "vmemKbytes" : [ 96309, 288929, 481549, 674168, 866788, 1059407, 1252028, 1417169, 1435861, 1432977, 1430092, 1427627 ],
      "physMemKbytes" : [ 20607, 61821, 103035, 144249, 185464, 226678, 267893, 302818, 304217, 300676, 297135, 294096 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13380,
        "virtualMemoryUsage" : 1461575680,
        "physicalMemoryUsage" : 300732416,
        "heapUsage" : 723517440
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059077,
    "taskID" : "task_1369942127770_1205_m_000073",
    "taskType" : "MAP",
    "finishTime" : 1371222116354,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2116.smile.com" ]
      },
      "hostName" : "/default-rack/a2116.smile.com",
      "startTime" : 1371222105211,
      "result" : "SUCCESS",
      "finishTime" : 1371222116354,
      "attemptID" : "attempt_1369942127770_1205_m_000073_0",
      "clockSplits" : [ 1066, 1066, 1066, 1066, 1066, 1066, 1066, 3425, 62, 62, 62, 62 ],
      "cpuUsages" : [ 983, 983, 983, 983, 983, 984, 983, 964, 826, 826, 826, 826 ],
      "vmemKbytes" : [ 91186, 273558, 455931, 638304, 820677, 1003049, 1185422, 1366539, 1437580, 1437580, 1437579, 1437580 ],
      "physMemKbytes" : [ 18730, 56190, 93651, 131111, 168572, 206032, 243493, 280753, 300503, 308951, 317398, 325847 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 11150,
        "virtualMemoryUsage" : 1472081920,
        "physicalMemoryUsage" : 337993728,
        "heapUsage" : 679804928
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059078,
    "taskID" : "task_1369942127770_1205_m_000074",
    "taskType" : "MAP",
    "finishTime" : 1371222117482,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2115.smile.com" ]
      },
      "hostName" : "/default-rack/a2115.smile.com",
      "startTime" : 1371222105212,
      "result" : "SUCCESS",
      "finishTime" : 1371222117482,
      "attemptID" : "attempt_1369942127770_1205_m_000074_0",
      "clockSplits" : [ 1130, 1131, 1131, 1131, 1131, 1131, 1130, 1087, 760, 760, 760, 983 ],
      "cpuUsages" : [ 1122, 1123, 1123, 1123, 1123, 1123, 1123, 1103, 953, 952, 952, 1210 ],
      "vmemKbytes" : [ 89422, 268266, 447110, 625954, 804799, 983643, 1162488, 1340061, 1409432, 1409432, 1409431, 1409432 ],
      "physMemKbytes" : [ 19797, 59392, 98988, 138583, 178180, 217775, 257371, 296665, 310308, 307505, 304701, 301898 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13030,
        "virtualMemoryUsage" : 1443258368,
        "physicalMemoryUsage" : 307699712,
        "heapUsage" : 723517440
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72236,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222059078,
    "taskID" : "task_1369942127770_1205_m_000075",
    "taskType" : "MAP",
    "finishTime" : 1371222122401,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2118.smile.com" ]
      },
      "hostName" : "/default-rack/a2118.smile.com",
      "startTime" : 1371222105213,
      "result" : "SUCCESS",
      "finishTime" : 1371222122401,
      "attemptID" : "attempt_1369942127770_1205_m_000075_0",
      "clockSplits" : [ 1858, 1858, 1858, 1858, 1859, 1408, 1202, 1156, 804, 803, 804, 1715 ],
      "cpuUsages" : [ 1273, 1274, 1274, 1274, 1274, 1159, 1106, 1069, 800, 799, 799, 1319 ],
      "vmemKbytes" : [ 133405, 400215, 667026, 933837, 1200648, 1404711, 1417880, 1417879, 1417880, 1417880, 1417879, 1417880 ],
      "physMemKbytes" : [ 28701, 86104, 143508, 200911, 258315, 302557, 306759, 308178, 307670, 305916, 304161, 302442 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13420,
        "virtualMemoryUsage" : 1451909120,
        "physicalMemoryUsage" : 309051392,
        "heapUsage" : 723582976
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059078,
    "taskID" : "task_1369942127770_1205_m_000076",
    "taskType" : "MAP",
    "finishTime" : 1371222122853,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2115.smile.com" ]
      },
      "hostName" : "/default-rack/a2115.smile.com",
      "startTime" : 1371222106215,
      "result" : "SUCCESS",
      "finishTime" : 1371222122853,
      "attemptID" : "attempt_1369942127770_1205_m_000076_0",
      "clockSplits" : [ 1403, 1403, 1403, 1403, 1403, 1403, 1403, 1741, 4606, 154, 155, 155 ],
      "cpuUsages" : [ 1148, 1149, 1149, 1149, 1149, 1149, 1149, 1159, 1170, 996, 996, 997 ],
      "vmemKbytes" : [ 91602, 274808, 458014, 641219, 824426, 1007631, 1190838, 1369307, 1423984, 1423984, 1423983, 1423984 ],
      "physMemKbytes" : [ 19851, 59554, 99258, 138961, 178666, 218369, 258073, 296821, 310558, 310242, 307937, 305632 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13360,
        "virtualMemoryUsage" : 1458159616,
        "physicalMemoryUsage" : 311787520,
        "heapUsage" : 723582976
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059079,
    "taskID" : "task_1369942127770_1205_m_000077",
    "taskType" : "MAP",
    "finishTime" : 1371222123218,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2117.smile.com" ]
      },
      "hostName" : "/default-rack/a2117.smile.com",
      "startTime" : 1371222106215,
      "result" : "SUCCESS",
      "finishTime" : 1371222123218,
      "attemptID" : "attempt_1369942127770_1205_m_000077_0",
      "clockSplits" : [ 2676, 2677, 2676, 2677, 821, 793, 794, 3489, 97, 97, 97, 97 ],
      "cpuUsages" : [ 1339, 1340, 1340, 1340, 957, 951, 951, 951, 975, 975, 975, 976 ],
      "vmemKbytes" : [ 176938, 530815, 884692, 1238569, 1420882, 1420923, 1420924, 1420923, 1420924, 1420924, 1420923, 1420924 ],
      "physMemKbytes" : [ 38335, 115005, 191676, 268346, 307977, 308255, 308526, 308795, 308516, 307709, 306902, 306095 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13070,
        "virtualMemoryUsage" : 1455026176,
        "physicalMemoryUsage" : 313028608,
        "heapUsage" : 723648512
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72236,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222059079,
    "taskID" : "task_1369942127770_1205_m_000078",
    "taskType" : "MAP",
    "finishTime" : 1371222127755,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2117.smile.com" ]
      },
      "hostName" : "/default-rack/a2117.smile.com",
      "startTime" : 1371222107218,
      "result" : "SUCCESS",
      "finishTime" : 1371222127755,
      "attemptID" : "attempt_1369942127770_1205_m_000078_0",
      "clockSplits" : [ 2466, 2467, 2466, 2467, 3087, 638, 583, 582, 583, 583, 1609, 3001 ],
      "cpuUsages" : [ 1319, 1319, 1320, 1319, 1177, 909, 903, 903, 902, 903, 786, 1120 ],
      "vmemKbytes" : [ 176823, 530469, 884116, 1237762, 1420607, 1420659, 1420660, 1420659, 1420660, 1420660, 1420659, 1420660 ],
      "physMemKbytes" : [ 37490, 112471, 187452, 262433, 299476, 297339, 296741, 296144, 295547, 294950, 294467, 294436 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 12880,
        "virtualMemoryUsage" : 1454755840,
        "physicalMemoryUsage" : 301457408,
        "heapUsage" : 723779584
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059079,
    "taskID" : "task_1369942127770_1205_m_000079",
    "taskType" : "MAP",
    "finishTime" : 1371222127589,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2117.smile.com" ]
      },
      "hostName" : "/default-rack/a2117.smile.com",
      "startTime" : 1371222107220,
      "result" : "SUCCESS",
      "finishTime" : 1371222127589,
      "attemptID" : "attempt_1369942127770_1205_m_000079_0",
      "clockSplits" : [ 2808, 2809, 2809, 2808, 850, 816, 816, 814, 796, 797, 797, 3445 ],
      "cpuUsages" : [ 1306, 1307, 1307, 1307, 1048, 1043, 1043, 1018, 839, 838, 838, 1256 ],
      "vmemKbytes" : [ 179463, 538389, 897316, 1256243, 1441819, 1441871, 1441872, 1441871, 1441872, 1441872, 1441871, 1441872 ],
      "physMemKbytes" : [ 38683, 116049, 193416, 270783, 311546, 313136, 314715, 316267, 315488, 313213, 310938, 308727 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13150,
        "virtualMemoryUsage" : 1476476928,
        "physicalMemoryUsage" : 315416576,
        "heapUsage" : 723582976
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059080,
    "taskID" : "task_1369942127770_1205_m_000080",
    "taskType" : "MAP",
    "finishTime" : 1371222123105,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2117.smile.com" ]
      },
      "hostName" : "/default-rack/a2117.smile.com",
      "startTime" : 1371222107219,
      "result" : "SUCCESS",
      "finishTime" : 1371222123105,
      "attemptID" : "attempt_1369942127770_1205_m_000080_0",
      "clockSplits" : [ 2261, 2262, 2262, 2262, 2198, 591, 591, 591, 591, 592, 1475, 205 ],
      "cpuUsages" : [ 1233, 1233, 1234, 1233, 1223, 948, 949, 948, 949, 948, 1055, 1087 ],
      "vmemKbytes" : [ 142013, 426040, 710068, 994095, 1277915, 1409291, 1409292, 1409291, 1409292, 1409292, 1409291, 1409292 ],
      "physMemKbytes" : [ 30773, 92321, 153868, 215415, 276919, 305910, 306883, 307856, 308829, 309802, 309923, 308075 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13040,
        "virtualMemoryUsage" : 1443115008,
        "physicalMemoryUsage" : 314482688,
        "heapUsage" : 742129664
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72236,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222059080,
    "taskID" : "task_1369942127770_1205_m_000081",
    "taskType" : "MAP",
    "finishTime" : 1371222132159,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2116.smile.com" ]
      },
      "hostName" : "/default-rack/a2116.smile.com",
      "startTime" : 1371222116240,
      "result" : "SUCCESS",
      "finishTime" : 1371222132159,
      "attemptID" : "attempt_1369942127770_1205_m_000081_0",
      "clockSplits" : [ 1059, 1060, 1060, 1060, 1059, 1060, 1060, 1410, 4727, 335, 334, 335 ],
      "cpuUsages" : [ 1089, 1090, 1090, 1090, 1090, 1090, 1090, 1091, 1008, 847, 847, 848 ],
      "vmemKbytes" : [ 91426, 274280, 457134, 639987, 822841, 1005694, 1188548, 1370102, 1441028, 1441028, 1441027, 1441028 ],
      "physMemKbytes" : [ 20740, 62221, 103704, 145185, 186667, 228148, 269631, 310818, 326794, 325251, 323278, 321306 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 12430,
        "virtualMemoryUsage" : 1475612672,
        "physicalMemoryUsage" : 327917568,
        "heapUsage" : 657260544
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059080,
    "taskID" : "task_1369942127770_1205_m_000082",
    "taskType" : "MAP",
    "finishTime" : 1371222132250,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2116.smile.com" ]
      },
      "hostName" : "/default-rack/a2116.smile.com",
      "startTime" : 1371222117244,
      "result" : "SUCCESS",
      "finishTime" : 1371222132250,
      "attemptID" : "attempt_1369942127770_1205_m_000082_0",
      "clockSplits" : [ 1229, 1230, 1230, 1230, 1229, 1230, 1230, 3866, 83, 84, 83, 84 ],
      "cpuUsages" : [ 1028, 1028, 1028, 1028, 1028, 1028, 1028, 1009, 919, 918, 919, 919 ],
      "vmemKbytes" : [ 92019, 276058, 460097, 644136, 828175, 1012214, 1196253, 1377655, 1441156, 1441156, 1441155, 1441156 ],
      "physMemKbytes" : [ 18161, 54484, 90809, 127132, 163456, 199779, 236103, 271895, 283917, 283136, 282355, 281574 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 11980,
        "virtualMemoryUsage" : 1475743744,
        "physicalMemoryUsage" : 287850496,
        "heapUsage" : 608370688
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059080,
    "taskID" : "task_1369942127770_1205_m_000083",
    "taskType" : "MAP",
    "finishTime" : 1371222134319,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2118.smile.com" ]
      },
      "hostName" : "/default-rack/a2118.smile.com",
      "startTime" : 1371222117244,
      "result" : "SUCCESS",
      "finishTime" : 1371222134319,
      "attemptID" : "attempt_1369942127770_1205_m_000083_0",
      "clockSplits" : [ 2126, 2126, 2126, 2126, 842, 820, 819, 813, 783, 783, 783, 2923 ],
      "cpuUsages" : [ 1199, 1200, 1200, 1200, 934, 930, 930, 909, 816, 816, 816, 1250 ],
      "vmemKbytes" : [ 178727, 536181, 893635, 1251088, 1434711, 1432295, 1429826, 1427400, 1426584, 1426584, 1426583, 1426584 ],
      "physMemKbytes" : [ 38394, 115182, 191970, 268758, 307779, 306378, 304967, 303565, 302585, 301819, 301052, 300280 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 12200,
        "virtualMemoryUsage" : 1460822016,
        "physicalMemoryUsage" : 307064832,
        "heapUsage" : 734920704
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72236,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222059081,
    "taskID" : "task_1369942127770_1205_m_000084",
    "taskType" : "MAP",
    "finishTime" : 1371222133861,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2116.smile.com" ]
      },
      "hostName" : "/default-rack/a2116.smile.com",
      "startTime" : 1371222118252,
      "result" : "SUCCESS",
      "finishTime" : 1371222133861,
      "attemptID" : "attempt_1369942127770_1205_m_000084_0",
      "clockSplits" : [ 2230, 2231, 2231, 2231, 750, 709, 709, 709, 803, 871, 871, 872 ],
      "cpuUsages" : [ 1179, 1179, 1180, 1179, 1004, 999, 998, 999, 861, 761, 760, 761 ],
      "vmemKbytes" : [ 178623, 535870, 893117, 1250364, 1438606, 1438739, 1438740, 1438739, 1438740, 1438740, 1438739, 1438740 ],
      "physMemKbytes" : [ 34739, 104219, 173699, 243178, 279472, 278825, 278153, 277480, 277015, 277474, 278043, 278614 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 12480,
        "virtualMemoryUsage" : 1473269760,
        "physicalMemoryUsage" : 284733440,
        "heapUsage" : 601817088
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059081,
    "taskID" : "task_1369942127770_1205_m_000085",
    "taskType" : "MAP",
    "finishTime" : 1371222139704,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2115.smile.com" ]
      },
      "hostName" : "/default-rack/a2115.smile.com",
      "startTime" : 1371222118252,
      "result" : "SUCCESS",
      "finishTime" : 1371222139704,
      "attemptID" : "attempt_1369942127770_1205_m_000085_0",
      "clockSplits" : [ 3454, 3455, 3455, 3454, 1009, 966, 966, 746, 682, 681, 682, 1895 ],
      "cpuUsages" : [ 1451, 1451, 1451, 1452, 1056, 1049, 1050, 858, 801, 801, 802, 1128 ],
      "vmemKbytes" : [ 177565, 532695, 887825, 1242955, 1426567, 1426619, 1426620, 1426619, 1426620, 1426620, 1426619, 1426620 ],
      "physMemKbytes" : [ 36583, 109749, 182916, 256083, 293999, 294187, 294366, 294292, 293645, 292976, 292306, 291648 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13350,
        "virtualMemoryUsage" : 1460858880,
        "physicalMemoryUsage" : 298389504,
        "heapUsage" : 723517440
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059081,
    "taskID" : "task_1369942127770_1205_m_000086",
    "taskType" : "MAP",
    "finishTime" : 1371222136354,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2118.smile.com" ]
      },
      "hostName" : "/default-rack/a2118.smile.com",
      "startTime" : 1371222118251,
      "result" : "SUCCESS",
      "finishTime" : 1371222136354,
      "attemptID" : "attempt_1369942127770_1205_m_000086_0",
      "clockSplits" : [ 3475, 3475, 3476, 711, 642, 643, 642, 677, 933, 934, 933, 1558 ],
      "cpuUsages" : [ 1468, 1468, 1468, 1016, 1005, 1005, 1004, 979, 787, 786, 786, 1068 ],
      "vmemKbytes" : [ 238103, 714311, 1190519, 1438076, 1434027, 1429835, 1425644, 1421481, 1419856, 1419856, 1419855, 1419856 ],
      "physMemKbytes" : [ 50419, 151257, 252095, 303534, 300615, 297664, 294714, 291769, 289364, 287302, 285239, 283851 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 12840,
        "virtualMemoryUsage" : 1453932544,
        "physicalMemoryUsage" : 290590720,
        "heapUsage" : 723451904
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72236,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222059082,
    "taskID" : "task_1369942127770_1205_m_000087",
    "taskType" : "MAP",
    "finishTime" : 1371222139789,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2115.smile.com" ]
      },
      "hostName" : "/default-rack/a2115.smile.com",
      "startTime" : 1371222118251,
      "result" : "SUCCESS",
      "finishTime" : 1371222139789,
      "attemptID" : "attempt_1369942127770_1205_m_000087_0",
      "clockSplits" : [ 3491, 3492, 3492, 3492, 853, 807, 807, 806, 796, 796, 796, 1904 ],
      "cpuUsages" : [ 1371, 1372, 1371, 1372, 1005, 999, 1000, 981, 848, 849, 848, 1114 ],
      "vmemKbytes" : [ 176529, 529588, 882647, 1235706, 1418247, 1418299, 1418300, 1418299, 1418300, 1418300, 1418299, 1418300 ],
      "physMemKbytes" : [ 37865, 113597, 189329, 265060, 304618, 305460, 306291, 307107, 306593, 305225, 303856, 302522 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13130,
        "virtualMemoryUsage" : 1452339200,
        "physicalMemoryUsage" : 309321728,
        "heapUsage" : 723582976
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059082,
    "taskID" : "task_1369942127770_1205_m_000088",
    "taskType" : "MAP",
    "finishTime" : 1371222136362,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2118.smile.com" ]
      },
      "hostName" : "/default-rack/a2118.smile.com",
      "startTime" : 1371222119253,
      "result" : "SUCCESS",
      "finishTime" : 1371222136362,
      "attemptID" : "attempt_1369942127770_1205_m_000088_0",
      "clockSplits" : [ 2893, 2894, 2894, 1311, 689, 690, 689, 743, 930, 931, 930, 1511 ],
      "cpuUsages" : [ 1313, 1313, 1313, 1052, 949, 949, 949, 909, 770, 769, 770, 1204 ],
      "vmemKbytes" : [ 214687, 644062, 1073437, 1392086, 1409140, 1409139, 1409140, 1409139, 1409140, 1409140, 1409139, 1409140 ],
      "physMemKbytes" : [ 46097, 138291, 230486, 299182, 303872, 304943, 306014, 307012, 305925, 303888, 301850, 300616 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 12260,
        "virtualMemoryUsage" : 1442959360,
        "physicalMemoryUsage" : 307785728,
        "heapUsage" : 723517440
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059082,
    "taskID" : "task_1369942127770_1205_m_000089",
    "taskType" : "MAP",
    "finishTime" : 1371222132666,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2117.smile.com" ]
      },
      "hostName" : "/default-rack/a2117.smile.com",
      "startTime" : 1371222119254,
      "result" : "SUCCESS",
      "finishTime" : 1371222132666,
      "attemptID" : "attempt_1369942127770_1205_m_000089_0",
      "clockSplits" : [ 1524, 1525, 1525, 1525, 1525, 1525, 596, 541, 542, 541, 541, 1496 ],
      "cpuUsages" : [ 1088, 1088, 1088, 1089, 1088, 1088, 829, 814, 813, 814, 813, 1188 ],
      "vmemKbytes" : [ 118410, 355230, 592051, 828872, 1065693, 1302514, 1433707, 1434071, 1434072, 1434072, 1434071, 1434072 ],
      "physMemKbytes" : [ 25455, 76367, 127278, 178189, 229102, 280013, 307852, 307113, 306294, 305475, 304656, 303834 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 11800,
        "virtualMemoryUsage" : 1468489728,
        "physicalMemoryUsage" : 310685696,
        "heapUsage" : 723517440
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72236,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222059082,
    "taskID" : "task_1369942127770_1205_m_000090",
    "taskType" : "MAP",
    "finishTime" : 1371222141107,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2115.smile.com" ]
      },
      "hostName" : "/default-rack/a2115.smile.com",
      "startTime" : 1371222119254,
      "result" : "SUCCESS",
      "finishTime" : 1371222141107,
      "attemptID" : "attempt_1369942127770_1205_m_000090_0",
      "clockSplits" : [ 3299, 3300, 3300, 3300, 1010, 971, 971, 735, 677, 677, 677, 2932 ],
      "cpuUsages" : [ 1468, 1469, 1469, 1468, 1113, 1106, 1106, 896, 845, 845, 844, 1181 ],
      "vmemKbytes" : [ 177790, 533371, 888953, 1244534, 1428379, 1428431, 1428432, 1428431, 1428432, 1428432, 1428431, 1428432 ],
      "physMemKbytes" : [ 36477, 109431, 182386, 255341, 293743, 295167, 296582, 297326, 296690, 296012, 295333, 294667 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13810,
        "virtualMemoryUsage" : 1462714368,
        "physicalMemoryUsage" : 301477888,
        "heapUsage" : 723582976
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059083,
    "taskID" : "task_1369942127770_1205_m_000091",
    "taskType" : "MAP",
    "finishTime" : 1371222135965,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2118.smile.com" ]
      },
      "hostName" : "/default-rack/a2118.smile.com",
      "startTime" : 1371222120259,
      "result" : "SUCCESS",
      "finishTime" : 1371222135965,
      "attemptID" : "attempt_1369942127770_1205_m_000091_0",
      "clockSplits" : [ 1451, 1451, 1451, 1451, 1451, 1451, 1451, 1451, 2873, 208, 208, 208 ],
      "cpuUsages" : [ 1031, 1032, 1031, 1032, 1031, 1032, 1031, 1032, 875, 811, 811, 811 ],
      "vmemKbytes" : [ 86236, 258709, 431183, 603656, 776130, 948603, 1121077, 1293550, 1422372, 1429552, 1429551, 1429552 ],
      "physMemKbytes" : [ 17759, 53278, 88799, 124318, 159839, 195358, 230879, 266398, 292713, 293373, 292518, 291663 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 11660,
        "virtualMemoryUsage" : 1463861248,
        "physicalMemoryUsage" : 298164224,
        "heapUsage" : 723582976
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059083,
    "taskID" : "task_1369942127770_1205_m_000092",
    "taskType" : "MAP",
    "finishTime" : 1371222141263,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2115.smile.com" ]
      },
      "hostName" : "/default-rack/a2115.smile.com",
      "startTime" : 1371222120260,
      "result" : "SUCCESS",
      "finishTime" : 1371222141263,
      "attemptID" : "attempt_1369942127770_1205_m_000092_0",
      "clockSplits" : [ 2790, 2790, 2790, 2790, 1207, 1183, 1013, 663, 663, 664, 663, 3419 ],
      "cpuUsages" : [ 1414, 1415, 1414, 1415, 1063, 1057, 992, 858, 858, 858, 858, 1138 ],
      "vmemKbytes" : [ 175507, 526522, 877537, 1228552, 1409390, 1409431, 1409432, 1409431, 1409432, 1409432, 1409431, 1409432 ],
      "physMemKbytes" : [ 37701, 113103, 188506, 263908, 302825, 302979, 303086, 302647, 302037, 301427, 300816, 300337 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13470,
        "virtualMemoryUsage" : 1443258368,
        "physicalMemoryUsage" : 307466240,
        "heapUsage" : 723910656
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72236,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222059083,
    "taskID" : "task_1369942127770_1205_m_000093",
    "taskType" : "MAP",
    "finishTime" : 1371222140945,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2115.smile.com" ]
      },
      "hostName" : "/default-rack/a2115.smile.com",
      "startTime" : 1371222121262,
      "result" : "SUCCESS",
      "finishTime" : 1371222140945,
      "attemptID" : "attempt_1369942127770_1205_m_000093_0",
      "clockSplits" : [ 2364, 2365, 2365, 2365, 2596, 1091, 628, 629, 628, 629, 2831, 383 ],
      "cpuUsages" : [ 1300, 1300, 1300, 1300, 1207, 990, 924, 925, 924, 925, 887, 878 ],
      "vmemKbytes" : [ 179076, 537228, 895381, 1253534, 1438050, 1437355, 1434909, 1432392, 1429877, 1427361, 1425627, 1425572 ],
      "physMemKbytes" : [ 38409, 115228, 192047, 268866, 308684, 308141, 305153, 302065, 298978, 295891, 293745, 293615 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13140,
        "virtualMemoryUsage" : 1459785728,
        "physicalMemoryUsage" : 300752896,
        "heapUsage" : 727515136
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059084,
    "taskID" : "task_1369942127770_1205_m_000094",
    "taskType" : "MAP",
    "finishTime" : 1371222136213,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2117.smile.com" ]
      },
      "hostName" : "/default-rack/a2117.smile.com",
      "startTime" : 1371222122266,
      "result" : "SUCCESS",
      "finishTime" : 1371222136213,
      "attemptID" : "attempt_1369942127770_1205_m_000094_0",
      "clockSplits" : [ 1233, 1233, 1234, 1233, 1234, 1233, 1233, 762, 671, 672, 672, 2532 ],
      "cpuUsages" : [ 980, 981, 980, 981, 980, 981, 981, 746, 702, 702, 702, 1204 ],
      "vmemKbytes" : [ 98416, 295249, 492083, 688916, 885750, 1082583, 1279417, 1406650, 1409140, 1409140, 1409139, 1409140 ],
      "physMemKbytes" : [ 21848, 65544, 109240, 152936, 196633, 240328, 284025, 311912, 311463, 310448, 309433, 308423 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 10920,
        "virtualMemoryUsage" : 1442959360,
        "physicalMemoryUsage" : 315342848,
        "heapUsage" : 723451904
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222059084,
    "taskID" : "task_1369942127770_1205_m_000095",
    "taskType" : "MAP",
    "finishTime" : 1371222141112,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2115.smile.com" ]
      },
      "hostName" : "/default-rack/a2115.smile.com",
      "startTime" : 1371222123269,
      "result" : "SUCCESS",
      "finishTime" : 1371222141112,
      "attemptID" : "attempt_1369942127770_1205_m_000095_0",
      "clockSplits" : [ 2087, 2088, 2087, 2088, 2088, 1195, 746, 746, 746, 2974, 245, 246 ],
      "cpuUsages" : [ 1246, 1246, 1247, 1246, 1247, 1130, 1071, 1071, 1071, 975, 875, 875 ],
      "vmemKbytes" : [ 132085, 396256, 660427, 924598, 1188769, 1394563, 1409404, 1409403, 1409404, 1409404, 1409403, 1409404 ],
      "physMemKbytes" : [ 28569, 85709, 142849, 199988, 257129, 301711, 305219, 305534, 305849, 305967, 304859, 303540 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13560,
        "virtualMemoryUsage" : 1443229696,
        "physicalMemoryUsage" : 310263808,
        "heapUsage" : 734920704
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72236,
    "outputRecords" : 4166666
  } ],
  "reduceTasks" : [ ],
  "launchTime" : 1371222058937,
  "totalMaps" : 96,
  "totalReduces" : 0,
  "otherTasks" : [ ],
  "jobProperties" : {
    "mapreduce.job.ubertask.enable" : "false",
    "yarn.resourcemanager.max-completed-applications" : "10000",
    "yarn.resourcemanager.delayed.delegation-token.removal-interval-ms" : "30000",
    "mapreduce.client.submit.file.replication" : "2",
    "yarn.nodemanager.container-manager.thread-count" : "20",
    "mapred.queue.default.acl-administer-jobs" : "*",
    "dfs.image.transfer.bandwidthPerSec" : "0",
    "mapreduce.tasktracker.healthchecker.interval" : "60000",
    "mapreduce.jobtracker.staging.root.dir" : "/user",
    "yarn.resourcemanager.recovery.enabled" : "false",
    "yarn.resourcemanager.am.max-retries" : "1",
    "dfs.block.access.token.lifetime" : "600",
    "fs.AbstractFileSystem.file.impl" : "org.apache.hadoop.fs.local.LocalFs",
    "mapreduce.client.completion.pollinterval" : "5000",
    "mapreduce.job.ubertask.maxreduces" : "1",
    "mapreduce.reduce.shuffle.memory.limit.percent" : "0.25",
    "dfs.domain.socket.path" : "/var/run/hdfs-sockets/dn",
    "hadoop.ssl.keystores.factory.class" : "org.apache.hadoop.security.ssl.FileBasedKeyStoresFactory",
    "hadoop.http.authentication.kerberos.keytab" : "${user.home}/hadoop.keytab",
    "yarn.nodemanager.keytab" : "/etc/krb5.keytab",
    "io.seqfile.sorter.recordlimit" : "1000000",
    "s3.blocksize" : "********",
    "mapreduce.task.io.sort.factor" : "10",
    "yarn.nodemanager.disk-health-checker.interval-ms" : "120000",
    "mapreduce.job.working.dir" : "hdfs://a2115.smile.com:8020/user/jenkins",
    "yarn.admin.acl" : "*",
    "mapreduce.job.speculative.speculativecap" : "0.1",
    "dfs.namenode.num.checkpoints.retained" : "2",
    "dfs.namenode.delegation.token.renew-interval" : "86400000",
    "yarn.nodemanager.resource.memory-mb" : "8192",
    "io.map.index.interval" : "128",
    "s3.client-write-packet-size" : "65536",
    "mapreduce.task.files.preserve.failedtasks" : "false",
    "dfs.namenode.http-address" : "a2115.smile.com:20101",
    "ha.zookeeper.session-timeout.ms" : "5000",
    "hadoop.hdfs.configuration.version" : "1",
    "s3.replication" : "3",
    "dfs.datanode.balance.bandwidthPerSec" : "1048576",
    "mapreduce.reduce.shuffle.connect.timeout" : "180000",
    "hadoop.ssl.enabled" : "false",
    "dfs.journalnode.rpc-address" : "0.0.0.0:8485",
    "yarn.nodemanager.aux-services" : "mapreduce.shuffle",
    "mapreduce.job.counters.max" : "120",
    "dfs.datanode.readahead.bytes" : "4193404",
    "ipc.client.connect.max.retries.on.timeouts" : "45",
    "mapreduce.job.complete.cancel.delegation.tokens" : "true",
    "dfs.client.failover.max.attempts" : "15",
    "dfs.namenode.checkpoint.dir" : "file://${hadoop.tmp.dir}/dfs/namesecondary",
    "dfs.namenode.replication.work.multiplier.per.iteration" : "2",
    "fs.trash.interval" : "1",
    "yarn.resourcemanager.admin.address" : "a2115.smile.com:8033",
    "ha.health-monitor.check-interval.ms" : "1000",
    "mapreduce.job.outputformat.class" : "org.apache.hadoop.examples.terasort.TeraOutputFormat",
    "hadoop.jetty.logs.serve.aliases" : "true",
    "hadoop.http.authentication.kerberos.principal" : "HTTP/_HOST@LOCALHOST",
    "mapreduce.tasktracker.taskmemorymanager.monitoringinterval" : "5000",
    "mapreduce.job.reduce.shuffle.consumer.plugin.class" : "org.apache.hadoop.mapreduce.task.reduce.Shuffle",
    "s3native.blocksize" : "********",
    "dfs.namenode.edits.dir" : "${dfs.namenode.name.dir}",
    "ha.health-monitor.sleep-after-disconnect.ms" : "1000",
    "dfs.encrypt.data.transfer" : "false",
    "dfs.datanode.http.address" : "0.0.0.0:50075",
    "mapreduce.terasort.num-rows" : "400000000",
    "mapreduce.job.map.class" : "org.apache.hadoop.examples.terasort.TeraGen$SortGenMapper",
    "mapreduce.jobtracker.jobhistory.task.numberprogresssplits" : "12",
    "dfs.namenode.write.stale.datanode.ratio" : "0.5f",
    "dfs.client.use.datanode.hostname" : "false",
    "yarn.acl.enable" : "true",
    "hadoop.security.instrumentation.requires.admin" : "false",
    "yarn.nodemanager.localizer.fetch.thread-count" : "4",
    "hadoop.security.authorization" : "false",
    "user.name" : "jenkins",
    "dfs.namenode.fs-limits.min-block-size" : "1048576",
    "dfs.client.failover.connection.retries.on.timeouts" : "0",
    "hadoop.security.group.mapping.ldap.search.filter.group" : "(objectClass=group)",
    "mapreduce.output.fileoutputformat.compress.codec" : "org.apache.hadoop.io.compress.DefaultCodec",
    "dfs.namenode.safemode.extension" : "30000",
    "mapreduce.shuffle.port" : "8080",
    "mapreduce.reduce.log.level" : "INFO",
    "yarn.log-aggregation-enable" : "false",
    "dfs.datanode.sync.behind.writes" : "false",
    "mapreduce.jobtracker.instrumentation" : "org.apache.hadoop.mapred.JobTrackerMetricsInst",
    "dfs.https.server.keystore.resource" : "ssl-server.xml",
    "hadoop.security.group.mapping.ldap.search.attr.group.name" : "cn",
    "dfs.namenode.replication.min" : "1",
    "mapreduce.map.java.opts" : " -Xmx825955249",
    "yarn.scheduler.fair.allocation.file" : "/etc/yarn/fair-scheduler.xml",
    "s3native.bytes-per-checksum" : "512",
    "mapreduce.tasktracker.tasks.sleeptimebeforesigkill" : "5000",
    "tfile.fs.output.buffer.size" : "262144",
    "yarn.nodemanager.local-dirs" : "${hadoop.tmp.dir}/nm-local-dir",
    "mapreduce.jobtracker.persist.jobstatus.active" : "false",
    "fs.AbstractFileSystem.hdfs.impl" : "org.apache.hadoop.fs.Hdfs",
    "mapreduce.job.map.output.collector.class" : "org.apache.hadoop.mapred.MapTask$MapOutputBuffer",
    "mapreduce.tasktracker.local.dir.minspacestart" : "0",
    "dfs.namenode.safemode.min.datanodes" : "0",
    "hadoop.security.uid.cache.secs" : "14400",
    "dfs.client.https.need-auth" : "false",
    "dfs.client.write.exclude.nodes.cache.expiry.interval.millis" : "600000",
    "dfs.client.https.keystore.resource" : "ssl-client.xml",
    "dfs.namenode.max.objects" : "0",
    "hadoop.ssl.client.conf" : "ssl-client.xml",
    "dfs.namenode.safemode.threshold-pct" : "0.999f",
    "mapreduce.tasktracker.local.dir.minspacekill" : "0",
    "mapreduce.jobtracker.retiredjobs.cache.size" : "1000",
    "dfs.blocksize" : "134217728",
    "yarn.resourcemanager.scheduler.class" : "org.apache.hadoop.yarn.server.resourcemanager.scheduler.fifo.FifoScheduler",
    "mapreduce.job.reduce.slowstart.completedmaps" : "0.8",
    "mapreduce.job.end-notification.retry.attempts" : "5",
    "mapreduce.job.inputformat.class" : "org.apache.hadoop.examples.terasort.TeraGen$RangeInputFormat",
    "mapreduce.map.memory.mb" : "1024",
    "mapreduce.job.user.name" : "jenkins",
    "mapreduce.tasktracker.outofband.heartbeat" : "false",
    "io.native.lib.available" : "true",
    "mapreduce.jobtracker.persist.jobstatus.hours" : "0",
    "dfs.client-write-packet-size" : "65536",
    "mapreduce.client.progressmonitor.pollinterval" : "1000",
    "dfs.namenode.name.dir" : "file://${hadoop.tmp.dir}/dfs/name",
    "dfs.ha.log-roll.period" : "120",
    "mapreduce.reduce.input.buffer.percent" : "0.0",
    "mapreduce.map.output.compress.codec" : "org.apache.hadoop.io.compress.SnappyCodec",
    "mapreduce.map.skip.proc.count.autoincr" : "true",
    "dfs.client.failover.sleep.base.millis" : "500",
    "dfs.datanode.directoryscan.threads" : "1",
    "mapreduce.jobtracker.address" : "neededForHive:999999",
    "mapreduce.cluster.local.dir" : "${hadoop.tmp.dir}/mapred/local",
    "yarn.scheduler.fair.user-as-default-queue" : "true",
    "mapreduce.job.application.attempt.id" : "1",
    "dfs.permissions.enabled" : "true",
    "mapreduce.tasktracker.taskcontroller" : "org.apache.hadoop.mapred.DefaultTaskController",
    "yarn.scheduler.fair.preemption" : "true",
    "mapreduce.reduce.shuffle.parallelcopies" : "5",
    "dfs.support.append" : "true",
    "yarn.nodemanager.env-whitelist" : "JAVA_HOME,HADOOP_COMMON_HOME,HADOOP_HDFS_HOME,HADOOP_CONF_DIR,YARN_HOME",
    "mapreduce.jobtracker.heartbeats.in.second" : "100",
    "mapreduce.job.maxtaskfailures.per.tracker" : "3",
    "ipc.client.connection.maxidletime" : "10000",
    "mapreduce.shuffle.ssl.enabled" : "false",
    "dfs.namenode.invalidate.work.pct.per.iteration" : "0.32f",
    "dfs.blockreport.intervalMsec" : "21600000",
    "fs.s3.sleepTimeSeconds" : "10",
    "dfs.namenode.replication.considerLoad" : "true",
    "dfs.client.block.write.retries" : "3",
    "hadoop.ssl.server.conf" : "ssl-server.xml",
    "dfs.namenode.name.dir.restore" : "false",
    "rpc.engine.org.apache.hadoop.mapreduce.v2.api.MRClientProtocolPB" : "org.apache.hadoop.ipc.ProtobufRpcEngine",
    "dfs.datanode.hdfs-blocks-metadata.enabled" : "true",
    "ha.zookeeper.parent-znode" : "/hadoop-ha",
    "io.seqfile.lazydecompress" : "true",
    "dfs.https.enable" : "false",
    "mapreduce.reduce.merge.inmem.threshold" : "1000",
    "mapreduce.input.fileinputformat.split.minsize" : "0",
    "dfs.replication" : "3",
    "ipc.client.tcpnodelay" : "false",
    "dfs.namenode.accesstime.precision" : "3600000",
    "s3.stream-buffer-size" : "4096",
    "mapreduce.jobtracker.tasktracker.maxblacklists" : "4",
    "dfs.client.read.shortcircuit.skip.checksum" : "false",
    "mapreduce.job.jvm.numtasks" : "1",
    "mapreduce.task.io.sort.mb" : "100",
    "io.file.buffer.size" : "65536",
    "dfs.namenode.audit.loggers" : "default",
    "dfs.namenode.checkpoint.txns" : "1000000",
    "yarn.nodemanager.admin-env" : "MALLOC_ARENA_MAX=$MALLOC_ARENA_MAX",
    "mapreduce.job.jar" : "/user/jenkins/.staging/job_1369942127770_1205/job.jar",
    "mapreduce.job.split.metainfo.maxsize" : "10000000",
    "kfs.replication" : "3",
    "rpc.engine.org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolPB" : "org.apache.hadoop.ipc.ProtobufRpcEngine",
    "yarn.app.mapreduce.am.scheduler.heartbeat.interval-ms" : "1000",
    "mapreduce.reduce.maxattempts" : "4",
    "kfs.stream-buffer-size" : "4096",
    "dfs.ha.tail-edits.period" : "60",
    "hadoop.security.authentication" : "simple",
    "fs.s3.buffer.dir" : "${hadoop.tmp.dir}/s3",
    "rpc.engine.org.apache.hadoop.yarn.api.AMRMProtocolPB" : "org.apache.hadoop.ipc.ProtobufRpcEngine",
    "mapreduce.jobtracker.taskscheduler" : "org.apache.hadoop.mapred.JobQueueTaskScheduler",
    "yarn.app.mapreduce.am.job.task.listener.thread-count" : "30",
    "dfs.namenode.avoid.read.stale.datanode" : "false",
    "mapreduce.job.reduces" : "0",
    "mapreduce.map.sort.spill.percent" : "0.8",
    "dfs.client.file-block-storage-locations.timeout" : "60",
    "dfs.datanode.drop.cache.behind.writes" : "false",
    "mapreduce.job.end-notification.retry.interval" : "1",
    "mapreduce.job.maps" : "96",
    "mapreduce.job.speculative.slownodethreshold" : "1.0",
    "tfile.fs.input.buffer.size" : "262144",
    "mapreduce.map.speculative" : "false",
    "dfs.block.access.token.enable" : "false",
    "dfs.journalnode.http-address" : "0.0.0.0:8480",
    "mapreduce.job.acl-view-job" : " ",
    "mapreduce.reduce.shuffle.retry-delay.max.ms" : "60000",
    "mapreduce.job.end-notification.max.retry.interval" : "5",
    "ftp.blocksize" : "********",
    "mapreduce.tasktracker.http.threads" : "80",
    "mapreduce.reduce.java.opts" : " -Xmx825955249",
    "dfs.datanode.data.dir" : "file://${hadoop.tmp.dir}/dfs/data",
    "ha.failover-controller.cli-check.rpc-timeout.ms" : "20000",
    "dfs.namenode.max.extra.edits.segments.retained" : "10000",
    "dfs.https.port" : "20102",
    "dfs.namenode.replication.interval" : "3",
    "mapreduce.task.skip.start.attempts" : "2",
    "dfs.namenode.https-address" : "a2115.smile.com:20102",
    "mapreduce.jobtracker.persist.jobstatus.dir" : "/jobtracker/jobsInfo",
    "ipc.client.kill.max" : "10",
    "dfs.ha.automatic-failover.enabled" : "false",
    "mapreduce.jobhistory.keytab" : "/etc/security/keytab/jhs.service.keytab",
    "dfs.image.transfer.timeout" : "600000",
    "dfs.client.failover.sleep.max.millis" : "15000",
    "mapreduce.job.end-notification.max.attempts" : "5",
    "mapreduce.task.tmp.dir" : "./tmp",
    "dfs.default.chunk.view.size" : "32768",
    "kfs.bytes-per-checksum" : "512",
    "mapreduce.reduce.memory.mb" : "1024",
    "hadoop.http.filter.initializers" : "org.apache.hadoop.yarn.server.webproxy.amfilter.AmFilterInitializer",
    "dfs.datanode.failed.volumes.tolerated" : "0",
    "hadoop.http.authentication.type" : "simple",
    "dfs.datanode.data.dir.perm" : "700",
    "yarn.resourcemanager.client.thread-count" : "50",
    "ipc.server.listen.queue.size" : "128",
    "mapreduce.reduce.skip.maxgroups" : "0",
    "file.stream-buffer-size" : "4096",
    "dfs.namenode.fs-limits.max-directory-items" : "0",
    "io.mapfile.bloom.size" : "1048576",
    "yarn.nodemanager.container-executor.class" : "org.apache.hadoop.yarn.server.nodemanager.DefaultContainerExecutor",
    "mapreduce.map.maxattempts" : "4",
    "mapreduce.jobtracker.jobhistory.block.size" : "3145728",
    "yarn.log-aggregation.retain-seconds" : "-1",
    "yarn.app.mapreduce.am.job.committer.cancel-timeout" : "60000",
    "ftp.replication" : "3",
    "mapreduce.jobtracker.http.address" : "0.0.0.0:50030",
    "yarn.nodemanager.health-checker.script.timeout-ms" : "1200000",
    "mapreduce.jobhistory.address" : "a2115.smile.com:10020",
    "mapreduce.jobtracker.taskcache.levels" : "2",
    "dfs.datanode.dns.nameserver" : "default",
    "mapreduce.application.classpath" : "$HADOOP_MAPRED_HOME/share/hadoop/mapreduce/*,$HADOOP_MAPRED_HOME/share/hadoop/mapreduce/lib/*",
    "yarn.nodemanager.log.retain-seconds" : "10800",
    "mapred.child.java.opts" : "-Xmx200m",
    "dfs.replication.max" : "512",
    "map.sort.class" : "org.apache.hadoop.util.QuickSort",
    "dfs.stream-buffer-size" : "4096",
    "dfs.namenode.backup.address" : "0.0.0.0:50100",
    "hadoop.util.hash.type" : "murmur",
    "dfs.block.access.key.update.interval" : "600",
    "mapreduce.reduce.skip.proc.count.autoincr" : "true",
    "dfs.datanode.dns.interface" : "default",
    "dfs.datanode.use.datanode.hostname" : "false",
    "mapreduce.job.output.key.class" : "org.apache.hadoop.io.Text",
    "dfs.client.read.shortcircuit" : "false",
    "dfs.namenode.backup.http-address" : "0.0.0.0:50105",
    "yarn.nodemanager.container-monitor.interval-ms" : "3000",
    "yarn.nodemanager.disk-health-checker.min-healthy-disks" : "0.25",
    "kfs.client-write-packet-size" : "65536",
    "ha.zookeeper.acl" : "world:anyone:rwcda",
    "yarn.nodemanager.sleep-delay-before-sigkill.ms" : "250",
    "mapreduce.job.dir" : "/user/jenkins/.staging/job_1369942127770_1205",
    "io.map.index.skip" : "0",
    "net.topology.node.switch.mapping.impl" : "org.apache.hadoop.net.ScriptBasedMapping",
    "fs.s3.maxRetries" : "4",
    "dfs.namenode.logging.level" : "info",
    "ha.failover-controller.new-active.rpc-timeout.ms" : "60000",
    "s3native.client-write-packet-size" : "65536",
    "yarn.resourcemanager.amliveliness-monitor.interval-ms" : "1000",
    "hadoop.http.staticuser.user" : "dr.who",
    "mapreduce.reduce.speculative" : "false",
    "mapreduce.client.output.filter" : "FAILED",
    "mapreduce.ifile.readahead.bytes" : "4194304",
    "mapreduce.tasktracker.report.address" : "127.0.0.1:0",
    "mapreduce.task.userlog.limit.kb" : "0",
    "mapreduce.tasktracker.map.tasks.maximum" : "2",
    "hadoop.http.authentication.simple.anonymous.allowed" : "true",
    "hadoop.fuse.timer.period" : "5",
    "dfs.namenode.num.extra.edits.retained" : "1000000",
    "hadoop.rpc.socket.factory.class.default" : "org.apache.hadoop.net.StandardSocketFactory",
    "mapreduce.job.submithostname" : "a2115.smile.com",
    "dfs.namenode.handler.count" : "10",
    "fs.automatic.close" : "false",
    "mapreduce.job.submithostaddress" : "*************",
    "mapreduce.tasktracker.healthchecker.script.timeout" : "600000",
    "dfs.datanode.directoryscan.interval" : "21600",
    "yarn.resourcemanager.address" : "a2115.smile.com:8032",
    "yarn.nodemanager.health-checker.interval-ms" : "600000",
    "dfs.client.file-block-storage-locations.num-threads" : "10",
    "yarn.resourcemanager.container-tokens.master-key-rolling-interval-secs" : "86400",
    "mapreduce.reduce.markreset.buffer.percent" : "0.0",
    "hadoop.security.group.mapping.ldap.directory.search.timeout" : "10000",
    "mapreduce.map.log.level" : "INFO",
    "dfs.bytes-per-checksum" : "512",
    "yarn.nodemanager.localizer.address" : "0.0.0.0:8040",
    "dfs.namenode.checkpoint.max-retries" : "3",
    "ha.health-monitor.rpc-timeout.ms" : "45000",
    "yarn.resourcemanager.keytab" : "/etc/krb5.keytab",
    "ftp.stream-buffer-size" : "4096",
    "dfs.namenode.avoid.write.stale.datanode" : "false",
    "hadoop.security.group.mapping.ldap.search.attr.member" : "member",
    "mapreduce.output.fileoutputformat.outputdir" : "hdfs://a2115.smile.com:8020/user/jenkins/tera-gen-1",
    "dfs.blockreport.initialDelay" : "0",
    "yarn.nm.liveness-monitor.expiry-interval-ms" : "600000",
    "hadoop.http.authentication.token.validity" : "36000",
    "dfs.namenode.delegation.token.max-lifetime" : "604800000",
    "mapreduce.job.hdfs-servers" : "${fs.defaultFS}",
    "s3native.replication" : "3",
    "yarn.nodemanager.localizer.client.thread-count" : "5",
    "dfs.heartbeat.interval" : "3",
    "rpc.engine.org.apache.hadoop.ipc.ProtocolMetaInfoPB" : "org.apache.hadoop.ipc.ProtobufRpcEngine",
    "dfs.ha.fencing.ssh.connect-timeout" : "30000",
    "yarn.resourcemanager.container.liveness-monitor.interval-ms" : "600000",
    "yarn.am.liveness-monitor.expiry-interval-ms" : "600000",
    "mapreduce.task.profile" : "false",
    "mapreduce.tasktracker.http.address" : "0.0.0.0:50060",
    "mapreduce.tasktracker.instrumentation" : "org.apache.hadoop.mapred.TaskTrackerMetricsInst",
    "mapreduce.jobhistory.webapp.address" : "a2115.smile.com:19888",
    "ha.failover-controller.graceful-fence.rpc-timeout.ms" : "5000",
    "yarn.ipc.rpc.class" : "org.apache.hadoop.yarn.ipc.HadoopYarnProtoRPC",
    "mapreduce.job.name" : "TeraGen",
    "kfs.blocksize" : "********",
    "yarn.resourcemanager.am-rm-tokens.master-key-rolling-interval-secs" : "86400",
    "mapreduce.job.ubertask.maxmaps" : "9",
    "yarn.scheduler.maximum-allocation-mb" : "8192",
    "yarn.nodemanager.heartbeat.interval-ms" : "1000",
    "mapreduce.job.userlog.retain.hours" : "24",
    "dfs.namenode.secondary.http-address" : "0.0.0.0:50090",
    "mapreduce.task.timeout" : "600000",
    "mapreduce.framework.name" : "yarn",
    "ipc.client.idlethreshold" : "4000",
    "ftp.bytes-per-checksum" : "512",
    "ipc.server.tcpnodelay" : "false",
    "dfs.namenode.stale.datanode.interval" : "30000",
    "s3.bytes-per-checksum" : "512",
    "mapreduce.job.speculative.slowtaskthreshold" : "1.0",
    "yarn.nodemanager.localizer.cache.target-size-mb" : "10240",
    "yarn.nodemanager.remote-app-log-dir" : "/tmp/logs",
    "fs.s3.block.size" : "********",
    "mapreduce.job.queuename" : "sls_queue_1",
    "dfs.client.failover.connection.retries" : "0",
    "hadoop.rpc.protection" : "authentication",
    "yarn.scheduler.minimum-allocation-mb" : "1024",
    "yarn.app.mapreduce.client-am.ipc.max-retries" : "1",
    "hadoop.security.auth_to_local" : "DEFAULT",
    "dfs.secondary.namenode.kerberos.internal.spnego.principal" : "${dfs.web.authentication.kerberos.principal}",
    "ftp.client-write-packet-size" : "65536",
    "fs.defaultFS" : "hdfs://a2115.smile.com:8020",
    "yarn.nodemanager.address" : "0.0.0.0:0",
    "yarn.scheduler.fair.assignmultiple" : "true",
    "yarn.resourcemanager.scheduler.client.thread-count" : "50",
    "mapreduce.task.merge.progress.records" : "10000",
    "file.client-write-packet-size" : "65536",
    "yarn.nodemanager.delete.thread-count" : "4",
    "yarn.resourcemanager.scheduler.address" : "a2115.smile.com:8030",
    "fs.trash.checkpoint.interval" : "0",
    "hadoop.http.authentication.signature.secret.file" : "${user.home}/hadoop-http-auth-signature-secret",
    "s3native.stream-buffer-size" : "4096",
    "mapreduce.reduce.shuffle.read.timeout" : "180000",
    "mapreduce.admin.user.env" : "LD_LIBRARY_PATH=$HADOOP_COMMON_HOME/lib/native",
    "yarn.app.mapreduce.am.command-opts" : " -Xmx1238932873",
    "mapreduce.local.clientfactory.class.name" : "org.apache.hadoop.mapred.LocalClientFactory",
    "dfs.namenode.checkpoint.edits.dir" : "${dfs.namenode.checkpoint.dir}",
    "fs.permissions.umask-mode" : "022",
    "dfs.client.domain.socket.data.traffic" : "false",
    "hadoop.common.configuration.version" : "0.23.0",
    "mapreduce.tasktracker.dns.interface" : "default",
    "mapreduce.output.fileoutputformat.compress.type" : "BLOCK",
    "mapreduce.ifile.readahead" : "true",
    "hadoop.security.group.mapping.ldap.ssl" : "false",
    "io.serializations" : "org.apache.hadoop.io.serializer.WritableSerialization,org.apache.hadoop.io.serializer.avro.AvroSpecificSerialization,org.apache.hadoop.io.serializer.avro.AvroReflectSerialization",
    "yarn.nodemanager.aux-services.mapreduce.shuffle.class" : "org.apache.hadoop.mapred.ShuffleHandler",
    "fs.df.interval" : "60000",
    "mapreduce.reduce.shuffle.input.buffer.percent" : "0.70",
    "io.seqfile.compress.blocksize" : "1000000",
    "hadoop.security.groups.cache.secs" : "300",
    "ipc.client.connect.max.retries" : "10",
    "dfs.namenode.delegation.key.update-interval" : "86400000",
    "yarn.nodemanager.process-kill-wait.ms" : "2000",
    "yarn.application.classpath" : "$HADOOP_CONF_DIR,$HADOOP_COMMON_HOME/*,$HADOOP_COMMON_HOME/lib/*,$HADOOP_HDFS_HOME/*,$HADOOP_HDFS_HOME/lib/*,$HADOOP_MAPRED_HOME/*,$HADOOP_MAPRED_HOME/lib/*,$YARN_HOME/*,$YARN_HOME/lib/*",
    "yarn.app.mapreduce.client.max-retries" : "3",
    "dfs.datanode.available-space-volume-choosing-policy.balanced-space-preference-fraction" : "0.75f",
    "yarn.nodemanager.log-aggregation.compression-type" : "none",
    "hadoop.security.group.mapping.ldap.search.filter.user" : "(&(objectClass=user)(sAMAccountName={0}))",
    "yarn.nodemanager.localizer.cache.cleanup.interval-ms" : "600000",
    "dfs.image.compress" : "false",
    "mapred.mapper.new-api" : "true",
    "yarn.nodemanager.log-dirs" : "${yarn.log.dir}/userlogs",
    "dfs.namenode.kerberos.internal.spnego.principal" : "${dfs.web.authentication.kerberos.principal}",
    "fs.s3n.block.size" : "********",
    "fs.ftp.host" : "0.0.0.0",
    "hadoop.security.group.mapping" : "org.apache.hadoop.security.JniBasedUnixGroupsMappingWithFallback",
    "dfs.datanode.address" : "0.0.0.0:50010",
    "mapreduce.map.skip.maxrecords" : "0",
    "dfs.datanode.https.address" : "0.0.0.0:50475",
    "file.replication" : "1",
    "yarn.resourcemanager.resource-tracker.address" : "a2115.smile.com:8031",
    "dfs.datanode.drop.cache.behind.reads" : "false",
    "hadoop.fuse.connection.timeout" : "300",
    "hadoop.work.around.non.threadsafe.getpwuid" : "false",
    "mapreduce.jobtracker.restart.recover" : "false",
    "hadoop.tmp.dir" : "/tmp/hadoop-${user.name}",
    "mapreduce.output.fileoutputformat.compress" : "false",
    "mapreduce.tasktracker.indexcache.mb" : "10",
    "mapreduce.client.genericoptionsparser.used" : "true",
    "dfs.client.block.write.replace-datanode-on-failure.policy" : "DEFAULT",
    "mapreduce.job.committer.setup.cleanup.needed" : "true",
    "hadoop.kerberos.kinit.command" : "kinit",
    "dfs.datanode.du.reserved" : "0",
    "dfs.namenode.fs-limits.max-blocks-per-file" : "1048576",
    "dfs.webhdfs.enabled" : "false",
    "file.bytes-per-checksum" : "512",
    "mapreduce.task.profile.reduces" : "0-2",
    "mapreduce.jobtracker.handler.count" : "10",
    "dfs.client.block.write.replace-datanode-on-failure.enable" : "true",
    "mapreduce.job.output.value.class" : "org.apache.hadoop.io.Text",
    "yarn.dispatcher.exit-on-error" : "true",
    "net.topology.script.number.args" : "100",
    "mapreduce.task.profile.maps" : "0-2",
    "dfs.namenode.decommission.interval" : "30",
    "dfs.image.compression.codec" : "org.apache.hadoop.io.compress.DefaultCodec",
    "yarn.resourcemanager.webapp.address" : "a2115.smile.com:8088",
    "mapreduce.jobtracker.system.dir" : "${hadoop.tmp.dir}/mapred/system",
    "hadoop.ssl.hostname.verifier" : "DEFAULT",
    "yarn.nodemanager.vmem-pmem-ratio" : "2.1",
    "dfs.namenode.support.allow.format" : "true",
    "mapreduce.jobhistory.principal" : "jhs/<EMAIL>",
    "io.mapfile.bloom.error.rate" : "0.005",
    "mapreduce.shuffle.ssl.file.buffer.size" : "65536",
    "dfs.permissions.superusergroup" : "supergroup",
    "dfs.datanode.available-space-volume-choosing-policy.balanced-space-threshold" : "10737418240",
    "mapreduce.jobtracker.expire.trackers.interval" : "600000",
    "mapreduce.cluster.acls.enabled" : "false",
    "yarn.nodemanager.remote-app-log-dir-suffix" : "logs",
    "ha.failover-controller.graceful-fence.connection.retries" : "1",
    "ha.health-monitor.connect-retry-interval.ms" : "1000",
    "mapreduce.reduce.shuffle.merge.percent" : "0.66",
    "yarn.app.mapreduce.am.resource.mb" : "1536",
    "io.seqfile.local.dir" : "${hadoop.tmp.dir}/io/local",
    "dfs.namenode.checkpoint.check.period" : "60",
    "yarn.resourcemanager.nm.liveness-monitor.interval-ms" : "1000",
    "mapreduce.jobtracker.maxtasks.perjob" : "-1",
    "mapreduce.jobtracker.jobhistory.lru.cache.size" : "5",
    "file.blocksize" : "********",
    "tfile.io.chunk.size" : "1048576",
    "mapreduce.job.acl-modify-job" : " ",
    "yarn.nodemanager.webapp.address" : "0.0.0.0:8042",
    "mapreduce.tasktracker.reduce.tasks.maximum" : "2",
    "io.skip.checksum.errors" : "false",
    "mapreduce.cluster.temp.dir" : "${hadoop.tmp.dir}/mapred/temp",
    "yarn.app.mapreduce.am.staging-dir" : "/user",
    "dfs.namenode.edits.journal-plugin.qjournal" : "org.apache.hadoop.hdfs.qjournal.client.QuorumJournalManager",
    "dfs.datanode.handler.count" : "10",
    "fs.ftp.host.port" : "21",
    "dfs.namenode.decommission.nodes.per.interval" : "5",
    "yarn.resourcemanager.admin.client.thread-count" : "1",
    "dfs.namenode.fs-limits.max-component-length" : "0",
    "dfs.namenode.checkpoint.period" : "3600",
    "fs.AbstractFileSystem.viewfs.impl" : "org.apache.hadoop.fs.viewfs.ViewFs",
    "yarn.resourcemanager.resource-tracker.client.thread-count" : "50",
    "mapreduce.tasktracker.dns.nameserver" : "default",
    "mapreduce.map.output.compress" : "true",
    "dfs.datanode.ipc.address" : "0.0.0.0:50020",
    "hadoop.ssl.require.client.cert" : "false",
    "yarn.nodemanager.delete.debug-delay-sec" : "0",
    "dfs.datanode.max.transfer.threads" : "4096"
  },
  "computonsPerMapInputByte" : -1,
  "computonsPerMapOutputByte" : -1,
  "computonsPerReduceInputByte" : -1,
  "computonsPerReduceOutputByte" : -1,
  "heapMegabytes" : 200,
  "outcome" : "SUCCESS",
  "jobtype" : "JAVA",
  "directDependantJobs" : [ ],
  "successfulMapAttemptCDFs" : [ {
    "maximum" : 9223372036854775807,
    "minimum" : -9223372036854775808,
    "rankings" : [ ],
    "numberValues" : 0
  }, {
    "maximum" : 9223372036854775807,
    "minimum" : -9223372036854775808,
    "rankings" : [ ],
    "numberValues" : 0
  }, {
    "maximum" : 9223372036854775807,
    "minimum" : -9223372036854775808,
    "rankings" : [ ],
    "numberValues" : 0
  }, {
    "maximum" : 47021,
    "minimum" : 11143,
    "rankings" : [ {
      "datum" : 13354,
      "relativeRanking" : 0.05
    }, {
      "datum" : 14101,
      "relativeRanking" : 0.1
    }, {
      "datum" : 15609,
      "relativeRanking" : 0.15
    }, {
      "datum" : 15919,
      "relativeRanking" : 0.2
    }, {
      "datum" : 17003,
      "relativeRanking" : 0.25
    }, {
      "datum" : 17109,
      "relativeRanking" : 0.3
    }, {
      "datum" : 18342,
      "relativeRanking" : 0.35
    }, {
      "datum" : 18870,
      "relativeRanking" : 0.4
    }, {
      "datum" : 19127,
      "relativeRanking" : 0.45
    }, {
      "datum" : 19221,
      "relativeRanking" : 0.5
    }, {
      "datum" : 19481,
      "relativeRanking" : 0.55
    }, {
      "datum" : 19896,
      "relativeRanking" : 0.6
    }, {
      "datum" : 20585,
      "relativeRanking" : 0.65
    }, {
      "datum" : 20784,
      "relativeRanking" : 0.7
    }, {
      "datum" : 21452,
      "relativeRanking" : 0.75
    }, {
      "datum" : 21853,
      "relativeRanking" : 0.8
    }, {
      "datum" : 22436,
      "relativeRanking" : 0.85
    }, {
      "datum" : 32646,
      "relativeRanking" : 0.9
    }, {
      "datum" : 41553,
      "relativeRanking" : 0.95
    } ],
    "numberValues" : 96
  } ],
  "failedMapAttemptCDFs" : [ {
    "maximum" : 9223372036854775807,
    "minimum" : -9223372036854775808,
    "rankings" : [ ],
    "numberValues" : 0
  }, {
    "maximum" : 9223372036854775807,
    "minimum" : -9223372036854775808,
    "rankings" : [ ],
    "numberValues" : 0
  }, {
    "maximum" : 9223372036854775807,
    "minimum" : -9223372036854775808,
    "rankings" : [ ],
    "numberValues" : 0
  }, {
    "maximum" : 9223372036854775807,
    "minimum" : -9223372036854775808,
    "rankings" : [ ],
    "numberValues" : 0
  } ],
  "successfulReduceAttemptCDF" : {
    "maximum" : 9223372036854775807,
    "minimum" : -9223372036854775808,
    "rankings" : [ ],
    "numberValues" : 0
  },
  "failedReduceAttemptCDF" : {
    "maximum" : 9223372036854775807,
    "minimum" : -9223372036854775808,
    "rankings" : [ ],
    "numberValues" : 0
  },
  "mapperTriesToSucceed" : [ 1.0 ],
  "failedMapperFraction" : 0.0,
  "relativeTime" : 0,
  "clusterMapMB" : -1,
  "clusterReduceMB" : -1,
  "jobMapMB" : 200,
  "jobReduceMB" : 200
} {
  "priority" : "NORMAL",
  "jobID" : "job_1369942127770_1206",
  "user" : "jenkins",
  "jobName" : "TeraGen",
  "submitTime" : 1371222159703,
  "finishTime" : 1371222251755,
  "queue" : "sls_queue_1",
  "mapTasks" : [ {
    "startTime" : 1371222164113,
    "taskID" : "task_1369942127770_1206_m_000000",
    "taskType" : "MAP",
    "finishTime" : 1371222188484,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2117.smile.com" ]
      },
      "hostName" : "/default-rack/a2117.smile.com",
      "startTime" : 1371222166321,
      "result" : "SUCCESS",
      "finishTime" : 1371222188484,
      "attemptID" : "attempt_1369942127770_1206_m_000000_0",
      "clockSplits" : [ 2278, 2279, 2279, 2279, 1641, 1090, 1090, 3620, 943, 943, 943, 2773 ],
      "cpuUsages" : [ 1480, 1481, 1481, 1481, 1298, 1141, 1141, 1312, 814, 815, 814, 1062 ],
      "vmemKbytes" : [ 161561, 484683, 807806, 1130929, 1407572, 1442299, 1442300, 1442299, 1442300, 1442300, 1442299, 1442300 ],
      "physMemKbytes" : [ 34370, 103111, 171853, 240594, 299471, 307004, 307167, 309072, 310772, 308485, 306197, 304712 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 82,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72234,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14320,
        "virtualMemoryUsage" : 1476915200,
        "physicalMemoryUsage" : 311963648,
        "heapUsage" : 735051776
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 82,
    "inputRecords" : 4166667,
    "outputBytes" : 72234,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164115,
    "taskID" : "task_1369942127770_1206_m_000001",
    "taskType" : "MAP",
    "finishTime" : 1371222185876,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2117.smile.com" ]
      },
      "hostName" : "/default-rack/a2117.smile.com",
      "startTime" : 1371222166287,
      "result" : "SUCCESS",
      "finishTime" : 1371222185876,
      "attemptID" : "attempt_1369942127770_1206_m_000001_0",
      "clockSplits" : [ 1967, 1967, 1967, 1967, 1968, 1160, 1022, 1021, 922, 887, 887, 3761 ],
      "cpuUsages" : [ 1290, 1290, 1290, 1291, 1290, 1137, 1110, 1111, 890, 812, 812, 1247 ],
      "vmemKbytes" : [ 136908, 410726, 684543, 958360, 1232179, 1406210, 1409140, 1409139, 1409140, 1409140, 1409139, 1409140 ],
      "physMemKbytes" : [ 29838, 89517, 149196, 208875, 268554, 307143, 309569, 311375, 312131, 310214, 308165, 306185 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 85,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72234,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13650,
        "virtualMemoryUsage" : 1442959360,
        "physicalMemoryUsage" : 312889344,
        "heapUsage" : 734986240
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 85,
    "inputRecords" : 4166667,
    "outputBytes" : 72234,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164115,
    "taskID" : "task_1369942127770_1206_m_000002",
    "taskType" : "MAP",
    "finishTime" : 1371222187434,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2117.smile.com" ]
      },
      "hostName" : "/default-rack/a2117.smile.com",
      "startTime" : 1371222166324,
      "result" : "SUCCESS",
      "finishTime" : 1371222187434,
      "attemptID" : "attempt_1369942127770_1206_m_000002_0",
      "clockSplits" : [ 2525, 2525, 2525, 2526, 1158, 1048, 1049, 3067, 3187, 40, 40, 40 ],
      "cpuUsages" : [ 1475, 1475, 1475, 1475, 1136, 1109, 1109, 1564, 795, 739, 739, 739 ],
      "vmemKbytes" : [ 174639, 523917, 873196, 1222474, 1422137, 1423103, 1423104, 1423103, 1423104, 1423104, 1423103, 1423104 ],
      "physMemKbytes" : [ 38111, 114334, 190557, 266780, 310216, 310109, 309790, 309622, 308756, 306687, 304614, 302541 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 85,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72234,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14130,
        "virtualMemoryUsage" : 1457258496,
        "physicalMemoryUsage" : 308678656,
        "heapUsage" : 740360192
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 85,
    "inputRecords" : 4166666,
    "outputBytes" : 72234,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222164115,
    "taskID" : "task_1369942127770_1206_m_000003",
    "taskType" : "MAP",
    "finishTime" : 1371222186091,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2117.smile.com" ]
      },
      "hostName" : "/default-rack/a2117.smile.com",
      "startTime" : 1371222166325,
      "result" : "SUCCESS",
      "finishTime" : 1371222186091,
      "attemptID" : "attempt_1369942127770_1206_m_000003_0",
      "clockSplits" : [ 2228, 2229, 2229, 2228, 1992, 1092, 1093, 1296, 4777, 199, 199, 199 ],
      "cpuUsages" : [ 1387, 1388, 1388, 1388, 1346, 1185, 1184, 1169, 1046, 879, 880, 880 ],
      "vmemKbytes" : [ 148980, 446940, 744901, 1042862, 1334323, 1427567, 1427568, 1427567, 1427568, 1427568, 1427567, 1427568 ],
      "physMemKbytes" : [ 31982, 95948, 159914, 223879, 286454, 306599, 306786, 306971, 307048, 307043, 307026, 307009 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 85,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72234,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14120,
        "virtualMemoryUsage" : 1461829632,
        "physicalMemoryUsage" : 314368000,
        "heapUsage" : 728760320
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 85,
    "inputRecords" : 4166667,
    "outputBytes" : 72234,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164116,
    "taskID" : "task_1369942127770_1206_m_000004",
    "taskType" : "MAP",
    "finishTime" : 1371222189053,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2117.smile.com" ]
      },
      "hostName" : "/default-rack/a2117.smile.com",
      "startTime" : 1371222166287,
      "result" : "SUCCESS",
      "finishTime" : 1371222189053,
      "attemptID" : "attempt_1369942127770_1206_m_000004_0",
      "clockSplits" : [ 2506, 2507, 2507, 2507, 1358, 1134, 1277, 3003, 1031, 1032, 3177, 718 ],
      "cpuUsages" : [ 1424, 1424, 1425, 1424, 1199, 1156, 1162, 1212, 826, 825, 851, 1032 ],
      "vmemKbytes" : [ 170607, 511823, 853038, 1194253, 1416017, 1420563, 1420564, 1420563, 1420564, 1420564, 1420563, 1420564 ],
      "physMemKbytes" : [ 36772, 110318, 183863, 257408, 305224, 306250, 306299, 306447, 304928, 302270, 299633, 298578 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 85,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72234,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13960,
        "virtualMemoryUsage" : 1454657536,
        "physicalMemoryUsage" : 305713152,
        "heapUsage" : 728367104
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 85,
    "inputRecords" : 4166667,
    "outputBytes" : 72234,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164116,
    "taskID" : "task_1369942127770_1206_m_000005",
    "taskType" : "MAP",
    "finishTime" : 1371222184979,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2117.smile.com" ]
      },
      "hostName" : "/default-rack/a2117.smile.com",
      "startTime" : 1371222166323,
      "result" : "SUCCESS",
      "finishTime" : 1371222184979,
      "attemptID" : "attempt_1369942127770_1206_m_000005_0",
      "clockSplits" : [ 1921, 1921, 1921, 1921, 1922, 1602, 1010, 1010, 1013, 1026, 1026, 2353 ],
      "cpuUsages" : [ 1306, 1306, 1306, 1306, 1306, 1250, 1146, 1146, 1053, 788, 788, 1199 ],
      "vmemKbytes" : [ 126096, 378289, 630482, 882675, 1134868, 1371662, 1425028, 1425027, 1425028, 1425028, 1425027, 1425028 ],
      "physMemKbytes" : [ 28145, 84436, 140727, 197018, 253310, 306166, 318110, 318149, 318111, 316467, 314188, 311974 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72234,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13900,
        "virtualMemoryUsage" : 1459228672,
        "physicalMemoryUsage" : 318754816,
        "heapUsage" : 734986240
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72234,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222164116,
    "taskID" : "task_1369942127770_1206_m_000006",
    "taskType" : "MAP",
    "finishTime" : 1371222185246,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2117.smile.com" ]
      },
      "hostName" : "/default-rack/a2117.smile.com",
      "startTime" : 1371222166324,
      "result" : "SUCCESS",
      "finishTime" : 1371222185246,
      "attemptID" : "attempt_1369942127770_1206_m_000006_0",
      "clockSplits" : [ 2131, 2131, 2132, 2131, 2083, 1125, 1125, 1164, 1326, 1326, 1702, 541 ],
      "cpuUsages" : [ 1351, 1351, 1351, 1351, 1344, 1220, 1220, 1130, 761, 760, 931, 960 ],
      "vmemKbytes" : [ 145579, 436739, 727899, 1019058, 1309880, 1441771, 1441772, 1441771, 1441772, 1441772, 1441771, 1441772 ],
      "physMemKbytes" : [ 31512, 94536, 157560, 220583, 283538, 313578, 316306, 318981, 319869, 319878, 318776, 315776 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72234,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13730,
        "virtualMemoryUsage" : 1476374528,
        "physicalMemoryUsage" : 321802240,
        "heapUsage" : 740491264
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72234,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164117,
    "taskID" : "task_1369942127770_1206_m_000007",
    "taskType" : "MAP",
    "finishTime" : 1371222185092,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2117.smile.com" ]
      },
      "hostName" : "/default-rack/a2117.smile.com",
      "startTime" : 1371222166325,
      "result" : "SUCCESS",
      "finishTime" : 1371222185092,
      "attemptID" : "attempt_1369942127770_1206_m_000007_0",
      "clockSplits" : [ 1853, 1853, 1853, 1854, 1853, 1487, 971, 970, 986, 1064, 1064, 2953 ],
      "cpuUsages" : [ 1287, 1287, 1288, 1287, 1287, 1189, 1049, 1049, 1004, 776, 776, 1151 ],
      "vmemKbytes" : [ 126150, 378452, 630754, 883055, 1135357, 1365947, 1409140, 1409139, 1409140, 1409140, 1409139, 1409140 ],
      "physMemKbytes" : [ 27651, 82956, 138261, 193565, 248870, 299639, 311257, 313850, 316375, 315596, 313007, 310500 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72234,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13430,
        "virtualMemoryUsage" : 1442959360,
        "physicalMemoryUsage" : 317206528,
        "heapUsage" : 740425728
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72234,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164117,
    "taskID" : "task_1369942127770_1206_m_000008",
    "taskType" : "MAP",
    "finishTime" : 1371222188434,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2115.smile.com" ]
      },
      "hostName" : "/default-rack/a2115.smile.com",
      "startTime" : 1371222166282,
      "result" : "SUCCESS",
      "finishTime" : 1371222188434,
      "attemptID" : "attempt_1369942127770_1206_m_000008_0",
      "clockSplits" : [ 2257, 2258, 2258, 2257, 1845, 1172, 1171, 3581, 845, 845, 845, 2798 ],
      "cpuUsages" : [ 1359, 1359, 1359, 1360, 1314, 1240, 1241, 1179, 860, 861, 861, 1097 ],
      "vmemKbytes" : [ 153669, 461007, 768346, 1075685, 1360846, 1419935, 1419936, 1419935, 1419936, 1419936, 1419935, 1419936 ],
      "physMemKbytes" : [ 34010, 102032, 170054, 238075, 301205, 314458, 314675, 315015, 314035, 311954, 309873, 308002 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72234,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14090,
        "virtualMemoryUsage" : 1454014464,
        "physicalMemoryUsage" : 315056128,
        "heapUsage" : 735051776
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72234,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222164117,
    "taskID" : "task_1369942127770_1206_m_000009",
    "taskType" : "MAP",
    "finishTime" : 1371222186078,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2115.smile.com" ]
      },
      "hostName" : "/default-rack/a2115.smile.com",
      "startTime" : 1371222166289,
      "result" : "SUCCESS",
      "finishTime" : 1371222186078,
      "attemptID" : "attempt_1369942127770_1206_m_000009_0",
      "clockSplits" : [ 2038, 2039, 2038, 2039, 2038, 1268, 1144, 1104, 802, 803, 803, 3666 ],
      "cpuUsages" : [ 1317, 1317, 1318, 1317, 1317, 1417, 1432, 1349, 722, 722, 723, 1209 ],
      "vmemKbytes" : [ 139866, 419600, 699334, 979067, 1258801, 1434828, 1437528, 1437527, 1437528, 1437528, 1437527, 1437528 ],
      "physMemKbytes" : [ 30358, 91075, 151792, 212509, 273226, 312566, 316176, 319207, 320182, 319829, 319476, 319130 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72234,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14160,
        "virtualMemoryUsage" : 1472028672,
        "physicalMemoryUsage" : 326660096,
        "heapUsage" : 740360192
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72234,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164118,
    "taskID" : "task_1369942127770_1206_m_000010",
    "taskType" : "MAP",
    "finishTime" : 1371222185273,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2115.smile.com" ]
      },
      "hostName" : "/default-rack/a2115.smile.com",
      "startTime" : 1371222166293,
      "result" : "SUCCESS",
      "finishTime" : 1371222185273,
      "attemptID" : "attempt_1369942127770_1206_m_000010_0",
      "clockSplits" : [ 1550, 1551, 1551, 1551, 1551, 1551, 1348, 982, 983, 1069, 1644, 3642 ],
      "cpuUsages" : [ 1196, 1197, 1197, 1197, 1197, 1197, 1161, 1097, 1097, 1062, 830, 1372 ],
      "vmemKbytes" : [ 106103, 318309, 530515, 742720, 954927, 1167133, 1365716, 1409403, 1409404, 1409404, 1409403, 1409404 ],
      "physMemKbytes" : [ 23450, 70351, 117253, 164154, 211056, 257957, 301852, 311562, 311632, 311701, 311734, 311749 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13800,
        "virtualMemoryUsage" : 1443229696,
        "physicalMemoryUsage" : 319262720,
        "heapUsage" : 728629248
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164118,
    "taskID" : "task_1369942127770_1206_m_000011",
    "taskType" : "MAP",
    "finishTime" : 1371222188434,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2115.smile.com" ]
      },
      "hostName" : "/default-rack/a2115.smile.com",
      "startTime" : 1371222166290,
      "result" : "SUCCESS",
      "finishTime" : 1371222188434,
      "attemptID" : "attempt_1369942127770_1206_m_000011_0",
      "clockSplits" : [ 2018, 2018, 2019, 2018, 2019, 1684, 1356, 6988, 74, 73, 74, 74 ],
      "cpuUsages" : [ 1424, 1425, 1425, 1424, 1425, 1364, 1305, 1424, 818, 819, 818, 819 ],
      "vmemKbytes" : [ 128226, 384681, 641136, 897591, 1154046, 1377893, 1409404, 1409403, 1409404, 1409404, 1409403, 1409404 ],
      "physMemKbytes" : [ 28599, 85797, 142996, 200194, 257393, 307333, 314455, 314599, 314625, 314232, 313838, 313445 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14790,
        "virtualMemoryUsage" : 1443229696,
        "physicalMemoryUsage" : 320749568,
        "heapUsage" : 740425728
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72236,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222164118,
    "taskID" : "task_1369942127770_1206_m_000012",
    "taskType" : "MAP",
    "finishTime" : 1371222185278,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2115.smile.com" ]
      },
      "hostName" : "/default-rack/a2115.smile.com",
      "startTime" : 1371222166295,
      "result" : "SUCCESS",
      "finishTime" : 1371222185278,
      "attemptID" : "attempt_1369942127770_1206_m_000012_0",
      "clockSplits" : [ 1913, 1914, 1914, 1914, 1914, 1611, 911, 910, 911, 1149, 1191, 2725 ],
      "cpuUsages" : [ 1375, 1376, 1376, 1376, 1376, 1301, 1127, 1127, 1128, 891, 850, 1257 ],
      "vmemKbytes" : [ 124608, 373827, 623045, 872262, 1121481, 1359363, 1420140, 1420139, 1420140, 1420140, 1420139, 1420140 ],
      "physMemKbytes" : [ 26214, 78645, 131075, 183505, 235936, 285986, 298857, 298969, 299082, 298216, 295652, 293146 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14560,
        "virtualMemoryUsage" : 1454223360,
        "physicalMemoryUsage" : 299470848,
        "heapUsage" : 740491264
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164119,
    "taskID" : "task_1369942127770_1206_m_000013",
    "taskType" : "MAP",
    "finishTime" : 1371222185273,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2115.smile.com" ]
      },
      "hostName" : "/default-rack/a2115.smile.com",
      "startTime" : 1371222166291,
      "result" : "SUCCESS",
      "finishTime" : 1371222185273,
      "attemptID" : "attempt_1369942127770_1206_m_000013_0",
      "clockSplits" : [ 1713, 1714, 1713, 1714, 1713, 1714, 1293, 1179, 1176, 1125, 1125, 2795 ],
      "cpuUsages" : [ 1221, 1221, 1221, 1221, 1221, 1221, 1236, 1241, 1229, 807, 807, 1234 ],
      "vmemKbytes" : [ 115735, 347206, 578677, 810148, 1041619, 1273090, 1433402, 1438795, 1438796, 1438796, 1438795, 1438796 ],
      "physMemKbytes" : [ 25299, 75897, 126496, 177095, 227694, 278292, 314180, 318040, 320785, 321923, 321617, 321326 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13880,
        "virtualMemoryUsage" : 1473327104,
        "physicalMemoryUsage" : 328986624,
        "heapUsage" : 740556800
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164120,
    "taskID" : "task_1369942127770_1206_m_000014",
    "taskType" : "MAP",
    "finishTime" : 1371222185953,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2115.smile.com" ]
      },
      "hostName" : "/default-rack/a2115.smile.com",
      "startTime" : 1371222166297,
      "result" : "SUCCESS",
      "finishTime" : 1371222185953,
      "attemptID" : "attempt_1369942127770_1206_m_000014_0",
      "clockSplits" : [ 1818, 1819, 1819, 1819, 1819, 1797, 1598, 1781, 4888, 164, 164, 164 ],
      "cpuUsages" : [ 1243, 1244, 1244, 1244, 1244, 1251, 1319, 1273, 930, 856, 856, 856 ],
      "vmemKbytes" : [ 121322, 363966, 606611, 849256, 1091901, 1333349, 1431760, 1431686, 1425453, 1421556, 1421555, 1421556 ],
      "physMemKbytes" : [ 26145, 78435, 130726, 183016, 235307, 287356, 310547, 313799, 309838, 305300, 303271, 301242 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13560,
        "virtualMemoryUsage" : 1455673344,
        "physicalMemoryUsage" : 307433472,
        "heapUsage" : 723845120
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72236,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222164120,
    "taskID" : "task_1369942127770_1206_m_000015",
    "taskType" : "MAP",
    "finishTime" : 1371222189058,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2115.smile.com" ]
      },
      "hostName" : "/default-rack/a2115.smile.com",
      "startTime" : 1371222166333,
      "result" : "SUCCESS",
      "finishTime" : 1371222189058,
      "attemptID" : "attempt_1369942127770_1206_m_000015_0",
      "clockSplits" : [ 2170, 2171, 2171, 2171, 2028, 1094, 1095, 4145, 798, 798, 798, 3281 ],
      "cpuUsages" : [ 1358, 1358, 1358, 1358, 1333, 1171, 1172, 1237, 815, 816, 815, 1259 ],
      "vmemKbytes" : [ 146393, 439180, 731967, 1024754, 1314962, 1425067, 1425068, 1425067, 1425068, 1425068, 1425067, 1425068 ],
      "physMemKbytes" : [ 32691, 98075, 163459, 228842, 293658, 318728, 319501, 320253, 319820, 318699, 317577, 316047 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14050,
        "virtualMemoryUsage" : 1459269632,
        "physicalMemoryUsage" : 320176128,
        "heapUsage" : 723648512
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164121,
    "taskID" : "task_1369942127770_1206_m_000016",
    "taskType" : "MAP",
    "finishTime" : 1371222185112,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2118.smile.com" ]
      },
      "hostName" : "/default-rack/a2118.smile.com",
      "startTime" : 1371222166298,
      "result" : "SUCCESS",
      "finishTime" : 1371222185112,
      "attemptID" : "attempt_1369942127770_1206_m_000016_0",
      "clockSplits" : [ 2147, 2147, 2148, 2147, 2054, 874, 874, 874, 919, 985, 986, 2651 ],
      "cpuUsages" : [ 1392, 1392, 1393, 1392, 1371, 1101, 1100, 1100, 1008, 870, 870, 1241 ],
      "vmemKbytes" : [ 144202, 432606, 721011, 1009416, 1297041, 1420819, 1420820, 1420819, 1420820, 1420820, 1420819, 1420820 ],
      "physMemKbytes" : [ 30336, 91008, 151680, 212351, 272862, 299357, 300156, 300954, 301511, 299852, 297656, 295530 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14230,
        "virtualMemoryUsage" : 1454919680,
        "physicalMemoryUsage" : 301989888,
        "heapUsage" : 734920704
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164121,
    "taskID" : "task_1369942127770_1206_m_000017",
    "taskType" : "MAP",
    "finishTime" : 1371222185125,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2118.smile.com" ]
      },
      "hostName" : "/default-rack/a2118.smile.com",
      "startTime" : 1371222166299,
      "result" : "SUCCESS",
      "finishTime" : 1371222185125,
      "attemptID" : "attempt_1369942127770_1206_m_000017_0",
      "clockSplits" : [ 2056, 2056, 2056, 2057, 2056, 1240, 1157, 1132, 945, 946, 945, 2174 ],
      "cpuUsages" : [ 1376, 1377, 1376, 1377, 1377, 1106, 1080, 1060, 914, 915, 915, 1267 ],
      "vmemKbytes" : [ 141624, 424872, 708121, 991370, 1274619, 1441166, 1442372, 1442371, 1442372, 1442372, 1442371, 1442372 ],
      "physMemKbytes" : [ 30671, 92015, 153358, 214701, 276045, 312210, 312702, 312916, 311552, 309174, 306795, 305251 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14140,
        "virtualMemoryUsage" : 1476988928,
        "physicalMemoryUsage" : 312541184,
        "heapUsage" : 723517440
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72236,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222164121,
    "taskID" : "task_1369942127770_1206_m_000018",
    "taskType" : "MAP",
    "finishTime" : 1371222185117,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2118.smile.com" ]
      },
      "hostName" : "/default-rack/a2118.smile.com",
      "startTime" : 1371222166301,
      "result" : "SUCCESS",
      "finishTime" : 1371222185117,
      "attemptID" : "attempt_1369942127770_1206_m_000018_0",
      "clockSplits" : [ 1934, 1934, 1934, 1934, 1934, 1203, 1012, 1012, 930, 897, 896, 3188 ],
      "cpuUsages" : [ 1359, 1360, 1359, 1360, 1359, 1168, 1117, 1117, 949, 878, 878, 1216 ],
      "vmemKbytes" : [ 138262, 414786, 691311, 967835, 1244360, 1434031, 1439980, 1439979, 1439980, 1439980, 1439979, 1439980 ],
      "physMemKbytes" : [ 30204, 90615, 151026, 211436, 271847, 313905, 317141, 319119, 320017, 317820, 315430, 313109 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14120,
        "virtualMemoryUsage" : 1474539520,
        "physicalMemoryUsage" : 319881216,
        "heapUsage" : 740294656
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164121,
    "taskID" : "task_1369942127770_1206_m_000019",
    "taskType" : "MAP",
    "finishTime" : 1371222186145,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2118.smile.com" ]
      },
      "hostName" : "/default-rack/a2118.smile.com",
      "startTime" : 1371222166303,
      "result" : "SUCCESS",
      "finishTime" : 1371222186145,
      "attemptID" : "attempt_1369942127770_1206_m_000019_0",
      "clockSplits" : [ 3008, 3009, 3009, 1882, 895, 895, 893, 819, 819, 819, 2062, 1653 ],
      "cpuUsages" : [ 1646, 1647, 1647, 1320, 1034, 1034, 1032, 934, 934, 935, 941, 966 ],
      "vmemKbytes" : [ 203217, 609652, 1016088, 1364807, 1409140, 1409139, 1409140, 1409139, 1409140, 1409140, 1409139, 1409140 ],
      "physMemKbytes" : [ 42475, 127426, 212377, 285272, 294585, 294637, 294690, 295048, 295688, 296328, 296949, 297136 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14150,
        "virtualMemoryUsage" : 1442959360,
        "physicalMemoryUsage" : 304185344,
        "heapUsage" : 740294656
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164122,
    "taskID" : "task_1369942127770_1206_m_000020",
    "taskType" : "MAP",
    "finishTime" : 1371222188294,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2118.smile.com" ]
      },
      "hostName" : "/default-rack/a2118.smile.com",
      "startTime" : 1371222166304,
      "result" : "SUCCESS",
      "finishTime" : 1371222188294,
      "attemptID" : "attempt_1369942127770_1206_m_000020_0",
      "clockSplits" : [ 2521, 2522, 2521, 2522, 1166, 875, 875, 3843, 796, 796, 796, 2750 ],
      "cpuUsages" : [ 1465, 1465, 1465, 1465, 1165, 1100, 1100, 1152, 745, 746, 745, 1157 ],
      "vmemKbytes" : [ 169996, 509988, 849980, 1189972, 1414887, 1420227, 1420228, 1420227, 1420228, 1420228, 1420227, 1420228 ],
      "physMemKbytes" : [ 37258, 111774, 186290, 260806, 310285, 311992, 312537, 313043, 312040, 310217, 308394, 306622 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13770,
        "virtualMemoryUsage" : 1454313472,
        "physicalMemoryUsage" : 313397248,
        "heapUsage" : 723648512
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72236,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222164122,
    "taskID" : "task_1369942127770_1206_m_000021",
    "taskType" : "MAP",
    "finishTime" : 1371222185112,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2118.smile.com" ]
      },
      "hostName" : "/default-rack/a2118.smile.com",
      "startTime" : 1371222166306,
      "result" : "SUCCESS",
      "finishTime" : 1371222185112,
      "attemptID" : "attempt_1369942127770_1206_m_000021_0",
      "clockSplits" : [ 1800, 1801, 1800, 1801, 1801, 1639, 1110, 1109, 1053, 976, 977, 2929 ],
      "cpuUsages" : [ 1297, 1297, 1297, 1297, 1297, 1248, 1084, 1085, 966, 809, 809, 1224 ],
      "vmemKbytes" : [ 125011, 375035, 625059, 875082, 1125106, 1368301, 1441708, 1441707, 1441708, 1441708, 1441707, 1441708 ],
      "physMemKbytes" : [ 27270, 81812, 136354, 190895, 245438, 298570, 316659, 319596, 322165, 321755, 320696, 319661 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13710,
        "virtualMemoryUsage" : 1476308992,
        "physicalMemoryUsage" : 326963200,
        "heapUsage" : 740360192
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164122,
    "taskID" : "task_1369942127770_1206_m_000022",
    "taskType" : "MAP",
    "finishTime" : 1371222186222,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2118.smile.com" ]
      },
      "hostName" : "/default-rack/a2118.smile.com",
      "startTime" : 1371222166307,
      "result" : "SUCCESS",
      "finishTime" : 1371222186222,
      "attemptID" : "attempt_1369942127770_1206_m_000022_0",
      "clockSplits" : [ 1673, 1674, 1674, 1674, 1674, 1674, 1281, 1231, 1150, 1000, 1000, 3869 ],
      "cpuUsages" : [ 1270, 1271, 1271, 1270, 1271, 1271, 1189, 1180, 1050, 811, 810, 1176 ],
      "vmemKbytes" : [ 116283, 348851, 581419, 813986, 1046555, 1279122, 1420532, 1422059, 1422060, 1422060, 1422059, 1422060 ],
      "physMemKbytes" : [ 24619, 73859, 123098, 172337, 221577, 270816, 300537, 300304, 299627, 297553, 295072, 292670 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13920,
        "virtualMemoryUsage" : 1456189440,
        "physicalMemoryUsage" : 298930176,
        "heapUsage" : 740425728
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164123,
    "taskID" : "task_1369942127770_1206_m_000023",
    "taskType" : "MAP",
    "finishTime" : 1371222186145,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2118.smile.com" ]
      },
      "hostName" : "/default-rack/a2118.smile.com",
      "startTime" : 1371222166345,
      "result" : "SUCCESS",
      "finishTime" : 1371222186145,
      "attemptID" : "attempt_1369942127770_1206_m_000023_0",
      "clockSplits" : [ 1890, 1890, 1890, 1890, 1890, 1603, 1310, 1285, 1093, 1093, 2285, 1599 ],
      "cpuUsages" : [ 1300, 1301, 1300, 1301, 1301, 1183, 1064, 1042, 878, 877, 908, 985 ],
      "vmemKbytes" : [ 130897, 392693, 654489, 916284, 1178080, 1407818, 1441216, 1441215, 1441216, 1441216, 1441215, 1441216 ],
      "physMemKbytes" : [ 28778, 86335, 143893, 201450, 259007, 309525, 316931, 316984, 315303, 312502, 309811, 309093 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13500,
        "virtualMemoryUsage" : 1475805184,
        "physicalMemoryUsage" : 316432384,
        "heapUsage" : 723517440
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72236,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222164123,
    "taskID" : "task_1369942127770_1206_m_000024",
    "taskType" : "MAP",
    "finishTime" : 1371222189046,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2116.smile.com" ]
      },
      "hostName" : "/default-rack/a2116.smile.com",
      "startTime" : 1371222166312,
      "result" : "SUCCESS",
      "finishTime" : 1371222189046,
      "attemptID" : "attempt_1369942127770_1206_m_000024_0",
      "clockSplits" : [ 2253, 2253, 2253, 2254, 1847, 969, 969, 7872, 514, 515, 514, 515 ],
      "cpuUsages" : [ 1355, 1356, 1356, 1355, 1273, 1091, 1092, 1191, 1000, 1000, 1000, 1001 ],
      "vmemKbytes" : [ 153860, 461581, 769302, 1077023, 1369366, 1441319, 1441320, 1441319, 1441320, 1441320, 1441319, 1441320 ],
      "physMemKbytes" : [ 30297, 90891, 151486, 212080, 269690, 284519, 285383, 286252, 287524, 289042, 290561, 292080 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14070,
        "virtualMemoryUsage" : 1475911680,
        "physicalMemoryUsage" : 299868160,
        "heapUsage" : 677838848
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164123,
    "taskID" : "task_1369942127770_1206_m_000025",
    "taskType" : "MAP",
    "finishTime" : 1371222185135,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2116.smile.com" ]
      },
      "hostName" : "/default-rack/a2116.smile.com",
      "startTime" : 1371222166313,
      "result" : "SUCCESS",
      "finishTime" : 1371222185135,
      "attemptID" : "attempt_1369942127770_1206_m_000025_0",
      "clockSplits" : [ 2136, 2136, 2136, 2137, 2130, 1075, 1075, 1048, 844, 844, 844, 2412 ],
      "cpuUsages" : [ 1387, 1387, 1388, 1387, 1386, 1053, 1052, 1035, 894, 895, 895, 1351 ],
      "vmemKbytes" : [ 142390, 427170, 711951, 996731, 1281508, 1422399, 1422400, 1422399, 1422400, 1422400, 1422399, 1422400 ],
      "physMemKbytes" : [ 26260, 78782, 131304, 183825, 236347, 262453, 262696, 262931, 262484, 261596, 260708, 259812 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14110,
        "virtualMemoryUsage" : 1456537600,
        "physicalMemoryUsage" : 265564160,
        "heapUsage" : 610598912
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164124,
    "taskID" : "task_1369942127770_1206_m_000026",
    "taskType" : "MAP",
    "finishTime" : 1371222185121,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2116.smile.com" ]
      },
      "hostName" : "/default-rack/a2116.smile.com",
      "startTime" : 1371222166319,
      "result" : "SUCCESS",
      "finishTime" : 1371222185121,
      "attemptID" : "attempt_1369942127770_1206_m_000026_0",
      "clockSplits" : [ 2178, 2179, 2178, 2179, 1980, 1194, 1194, 904, 713, 713, 713, 2671 ],
      "cpuUsages" : [ 1365, 1365, 1365, 1365, 1309, 1089, 1089, 917, 805, 805, 805, 1301 ],
      "vmemKbytes" : [ 147880, 443640, 739400, 1035160, 1324892, 1419083, 1419084, 1419083, 1419084, 1419084, 1419083, 1419084 ],
      "physMemKbytes" : [ 29159, 87477, 145795, 204112, 261297, 281682, 284343, 286121, 284301, 282096, 279891, 278128 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13580,
        "virtualMemoryUsage" : 1453142016,
        "physicalMemoryUsage" : 286785536,
        "heapUsage" : 609091584
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72236,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222164124,
    "taskID" : "task_1369942127770_1206_m_000027",
    "taskType" : "MAP",
    "finishTime" : 1371222186069,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2116.smile.com" ]
      },
      "hostName" : "/default-rack/a2116.smile.com",
      "startTime" : 1371222166318,
      "result" : "SUCCESS",
      "finishTime" : 1371222186069,
      "attemptID" : "attempt_1369942127770_1206_m_000027_0",
      "clockSplits" : [ 2248, 2249, 2249, 2248, 1776, 1068, 1067, 993, 918, 918, 2288, 1622 ],
      "cpuUsages" : [ 1367, 1368, 1367, 1368, 1258, 1097, 1096, 966, 836, 836, 879, 1172 ],
      "vmemKbytes" : [ 154759, 464278, 773798, 1083317, 1368023, 1423659, 1423660, 1423659, 1423660, 1423660, 1423659, 1423660 ],
      "physMemKbytes" : [ 30750, 92253, 153756, 215259, 271847, 283056, 283244, 283187, 281664, 279894, 278154, 278631 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13680,
        "virtualMemoryUsage" : 1457827840,
        "physicalMemoryUsage" : 286220288,
        "heapUsage" : 592445440
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164124,
    "taskID" : "task_1369942127770_1206_m_000028",
    "taskType" : "MAP",
    "finishTime" : 1371222185115,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2116.smile.com" ]
      },
      "hostName" : "/default-rack/a2116.smile.com",
      "startTime" : 1371222166327,
      "result" : "SUCCESS",
      "finishTime" : 1371222185115,
      "attemptID" : "attempt_1369942127770_1206_m_000028_0",
      "clockSplits" : [ 2169, 2170, 2170, 2170, 2072, 953, 954, 953, 890, 876, 876, 2529 ],
      "cpuUsages" : [ 1402, 1402, 1403, 1402, 1374, 1043, 1042, 1043, 920, 896, 895, 1618 ],
      "vmemKbytes" : [ 146031, 438093, 730155, 1022217, 1313341, 1436891, 1436892, 1436891, 1436892, 1436892, 1436891, 1436892 ],
      "physMemKbytes" : [ 28835, 86506, 144177, 201848, 259347, 285982, 289861, 293740, 300259, 311707, 323267, 334513 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14440,
        "virtualMemoryUsage" : 1471377408,
        "physicalMemoryUsage" : 346238976,
        "heapUsage" : 679346176
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164124,
    "taskID" : "task_1369942127770_1206_m_000029",
    "taskType" : "MAP",
    "finishTime" : 1371222186085,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2116.smile.com" ]
      },
      "hostName" : "/default-rack/a2116.smile.com",
      "startTime" : 1371222166330,
      "result" : "SUCCESS",
      "finishTime" : 1371222186085,
      "attemptID" : "attempt_1369942127770_1206_m_000029_0",
      "clockSplits" : [ 2131, 2132, 2132, 2132, 2131, 1196, 1106, 1107, 1114, 1115, 2070, 1384 ],
      "cpuUsages" : [ 1316, 1317, 1317, 1317, 1317, 1079, 1056, 1048, 986, 985, 1017, 1075 ],
      "vmemKbytes" : [ 141371, 424113, 706855, 989597, 1272340, 1437442, 1438532, 1438531, 1438532, 1438532, 1438531, 1438532 ],
      "physMemKbytes" : [ 27222, 81669, 136115, 190561, 245008, 277502, 279390, 281042, 280013, 277262, 274876, 276619 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13830,
        "virtualMemoryUsage" : 1473056768,
        "physicalMemoryUsage" : 284745728,
        "heapUsage" : 608043008
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72236,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222164125,
    "taskID" : "task_1369942127770_1206_m_000030",
    "taskType" : "MAP",
    "finishTime" : 1371222209777,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2117.smile.com" ]
      },
      "hostName" : "/default-rack/a2117.smile.com",
      "startTime" : 1371222186223,
      "result" : "SUCCESS",
      "finishTime" : 1371222209777,
      "attemptID" : "attempt_1369942127770_1206_m_000030_0",
      "clockSplits" : [ 3325, 3325, 3326, 1633, 1137, 1137, 1676, 1628, 916, 916, 917, 3460 ],
      "cpuUsages" : [ 1614, 1615, 1615, 1232, 1121, 1121, 1189, 1174, 1017, 1017, 1017, 1238 ],
      "vmemKbytes" : [ 220995, 662987, 1104979, 1414799, 1426152, 1426151, 1426152, 1426151, 1426152, 1426152, 1426151, 1426152 ],
      "physMemKbytes" : [ 46111, 138333, 230555, 295761, 299962, 301841, 304098, 306831, 306453, 304076, 301699, 299750 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 15080,
        "virtualMemoryUsage" : 1460379648,
        "physicalMemoryUsage" : 306688000,
        "heapUsage" : 745209856
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164125,
    "taskID" : "task_1369942127770_1206_m_000031",
    "taskType" : "MAP",
    "finishTime" : 1371222209814,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2117.smile.com" ]
      },
      "hostName" : "/default-rack/a2117.smile.com",
      "startTime" : 1371222186224,
      "result" : "SUCCESS",
      "finishTime" : 1371222209814,
      "attemptID" : "attempt_1369942127770_1206_m_000031_0",
      "clockSplits" : [ 2788, 2789, 2789, 2665, 1430, 1431, 1710, 1647, 900, 899, 900, 3438 ],
      "cpuUsages" : [ 1386, 1387, 1387, 1361, 1109, 1109, 1131, 1115, 964, 965, 964, 1252 ],
      "vmemKbytes" : [ 182198, 546594, 910990, 1273865, 1424288, 1424287, 1424288, 1424287, 1424288, 1424288, 1424287, 1424288 ],
      "physMemKbytes" : [ 38354, 115062, 191770, 268152, 298904, 297349, 296494, 296717, 296215, 295239, 294262, 293471 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14390,
        "virtualMemoryUsage" : 1458470912,
        "physicalMemoryUsage" : 302673920,
        "heapUsage" : 724500480
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164125,
    "taskID" : "task_1369942127770_1206_m_000032",
    "taskType" : "MAP",
    "finishTime" : 1371222209796,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2117.smile.com" ]
      },
      "hostName" : "/default-rack/a2117.smile.com",
      "startTime" : 1371222186230,
      "result" : "SUCCESS",
      "finishTime" : 1371222209796,
      "attemptID" : "attempt_1369942127770_1206_m_000032_0",
      "clockSplits" : [ 3446, 3446, 3447, 1666, 1610, 1052, 1052, 1212, 2392, 1340, 1113, 1783 ],
      "cpuUsages" : [ 1574, 1575, 1575, 1192, 1179, 1036, 1036, 1060, 1242, 897, 823, 1161 ],
      "vmemKbytes" : [ 236125, 708377, 1180629, 1421325, 1421348, 1421347, 1421348, 1421347, 1421348, 1421348, 1421347, 1421348 ],
      "physMemKbytes" : [ 51196, 153591, 255986, 308231, 308353, 309496, 311445, 313379, 314141, 313513, 311665, 309787 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14350,
        "virtualMemoryUsage" : 1455460352,
        "physicalMemoryUsage" : 316284928,
        "heapUsage" : 741015552
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72236,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222164126,
    "taskID" : "task_1369942127770_1206_m_000033",
    "taskType" : "MAP",
    "finishTime" : 1371222209114,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2116.smile.com" ]
      },
      "hostName" : "/default-rack/a2116.smile.com",
      "startTime" : 1371222186269,
      "result" : "SUCCESS",
      "finishTime" : 1371222209114,
      "attemptID" : "attempt_1369942127770_1206_m_000033_0",
      "clockSplits" : [ 2867, 2868, 2867, 2868, 1157, 1130, 1219, 2051, 1397, 925, 924, 2567 ],
      "cpuUsages" : [ 1397, 1397, 1397, 1397, 1133, 1129, 1138, 1227, 1043, 911, 911, 1420 ],
      "vmemKbytes" : [ 177507, 532523, 887539, 1242554, 1425454, 1425495, 1425496, 1425495, 1425496, 1425496, 1425495, 1425496 ],
      "physMemKbytes" : [ 35271, 105813, 176355, 246896, 284278, 286428, 288564, 289767, 290547, 292311, 294191, 296014 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14500,
        "virtualMemoryUsage" : 1459707904,
        "physicalMemoryUsage" : 303673344,
        "heapUsage" : 669974528
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164126,
    "taskID" : "task_1369942127770_1206_m_000034",
    "taskType" : "MAP",
    "finishTime" : 1371222205072,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2116.smile.com" ]
      },
      "hostName" : "/default-rack/a2116.smile.com",
      "startTime" : 1371222186230,
      "result" : "SUCCESS",
      "finishTime" : 1371222205072,
      "attemptID" : "attempt_1369942127770_1206_m_000034_0",
      "clockSplits" : [ 1942, 1943, 1943, 1942, 1943, 1831, 787, 788, 788, 889, 1494, 2545 ],
      "cpuUsages" : [ 1226, 1226, 1227, 1226, 1227, 1208, 1040, 1039, 1040, 1009, 828, 1724 ],
      "vmemKbytes" : [ 121272, 363818, 606363, 848908, 1091454, 1332855, 1430977, 1429650, 1428324, 1427011, 1426523, 1426524 ],
      "physMemKbytes" : [ 27249, 81750, 136250, 190750, 245251, 299492, 321349, 320728, 320107, 319489, 319062, 318747 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14020,
        "virtualMemoryUsage" : 1460760576,
        "physicalMemoryUsage" : 326189056,
        "heapUsage" : 668139520
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164126,
    "taskID" : "task_1369942127770_1206_m_000035",
    "taskType" : "MAP",
    "finishTime" : 1371222209265,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2116.smile.com" ]
      },
      "hostName" : "/default-rack/a2116.smile.com",
      "startTime" : 1371222186231,
      "result" : "SUCCESS",
      "finishTime" : 1371222209265,
      "attemptID" : "attempt_1369942127770_1206_m_000035_0",
      "clockSplits" : [ 2312, 2313, 2313, 2313, 2313, 2097, 1450, 1407, 1083, 1084, 3675, 312 ],
      "cpuUsages" : [ 1166, 1167, 1167, 1166, 1167, 1149, 1093, 1075, 945, 945, 1023, 1307 ],
      "vmemKbytes" : [ 123562, 370687, 617812, 864937, 1112063, 1351529, 1421224, 1421223, 1421224, 1421224, 1421223, 1421224 ],
      "physMemKbytes" : [ 23849, 71548, 119247, 166946, 214646, 260983, 277134, 280864, 282024, 281525, 281038, 280933 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13460,
        "virtualMemoryUsage" : 1455333376,
        "physicalMemoryUsage" : 293855232,
        "heapUsage" : 645595136
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72236,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222164127,
    "taskID" : "task_1369942127770_1206_m_000036",
    "taskType" : "MAP",
    "finishTime" : 1371222205027,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2118.smile.com" ]
      },
      "hostName" : "/default-rack/a2118.smile.com",
      "startTime" : 1371222186270,
      "result" : "SUCCESS",
      "finishTime" : 1371222205027,
      "attemptID" : "attempt_1369942127770_1206_m_000036_0",
      "clockSplits" : [ 1457, 1458, 1457, 1458, 1458, 1457, 1588, 1594, 1020, 1007, 1007, 3788 ],
      "cpuUsages" : [ 1146, 1146, 1146, 1146, 1147, 1146, 1350, 1361, 1032, 1024, 1024, 1372 ],
      "vmemKbytes" : [ 117533, 352601, 587669, 822736, 1057804, 1292871, 1420975, 1421223, 1421224, 1421224, 1421223, 1421224 ],
      "physMemKbytes" : [ 25662, 76986, 128310, 179634, 230959, 282283, 311405, 313987, 314079, 311498, 308915, 307356 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14040,
        "virtualMemoryUsage" : 1455333376,
        "physicalMemoryUsage" : 314486784,
        "heapUsage" : 725483520
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164127,
    "taskID" : "task_1369942127770_1206_m_000037",
    "taskType" : "MAP",
    "finishTime" : 1371222204802,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2118.smile.com" ]
      },
      "hostName" : "/default-rack/a2118.smile.com",
      "startTime" : 1371222186229,
      "result" : "SUCCESS",
      "finishTime" : 1371222204802,
      "attemptID" : "attempt_1369942127770_1206_m_000037_0",
      "clockSplits" : [ 2817, 2817, 2817, 1469, 1026, 1027, 1211, 1334, 3276, 257, 257, 258 ],
      "cpuUsages" : [ 1465, 1466, 1466, 1237, 1161, 1162, 1220, 1260, 1163, 850, 850, 850 ],
      "vmemKbytes" : [ 221437, 664313, 1107189, 1424591, 1438128, 1438127, 1438128, 1438127, 1438128, 1438128, 1438127, 1438128 ],
      "physMemKbytes" : [ 47520, 142560, 237600, 305733, 308702, 308767, 308909, 309355, 309754, 308145, 305672, 303200 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14150,
        "virtualMemoryUsage" : 1472643072,
        "physicalMemoryUsage" : 309211136,
        "heapUsage" : 723648512
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164127,
    "taskID" : "task_1369942127770_1206_m_000038",
    "taskType" : "MAP",
    "finishTime" : 1371222205071,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2118.smile.com" ]
      },
      "hostName" : "/default-rack/a2118.smile.com",
      "startTime" : 1371222186231,
      "result" : "SUCCESS",
      "finishTime" : 1371222205071,
      "attemptID" : "attempt_1369942127770_1206_m_000038_0",
      "clockSplits" : [ 2279, 2280, 2280, 2280, 869, 832, 833, 833, 889, 890, 889, 3531 ],
      "cpuUsages" : [ 1331, 1332, 1332, 1332, 1164, 1161, 1160, 1159, 1012, 1011, 1012, 1254 ],
      "vmemKbytes" : [ 176677, 530031, 883386, 1236740, 1422008, 1422115, 1422116, 1422115, 1422116, 1422116, 1422115, 1422116 ],
      "physMemKbytes" : [ 37600, 112801, 188002, 263203, 303425, 305112, 306777, 308442, 309142, 308918, 308694, 308464 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14390,
        "virtualMemoryUsage" : 1456246784,
        "physicalMemoryUsage" : 315645952,
        "heapUsage" : 745013248
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72236,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222164127,
    "taskID" : "task_1369942127770_1206_m_000039",
    "taskType" : "MAP",
    "finishTime" : 1371222204844,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2118.smile.com" ]
      },
      "hostName" : "/default-rack/a2118.smile.com",
      "startTime" : 1371222186270,
      "result" : "SUCCESS",
      "finishTime" : 1371222204844,
      "attemptID" : "attempt_1369942127770_1206_m_000039_0",
      "clockSplits" : [ 1520, 1521, 1521, 1520, 1521, 1502, 1411, 1412, 1054, 1023, 1024, 3236 ],
      "cpuUsages" : [ 1201, 1201, 1201, 1201, 1201, 1219, 1301, 1301, 1078, 1060, 1059, 997 ],
      "vmemKbytes" : [ 123345, 370038, 616730, 863421, 1110114, 1353144, 1437648, 1437647, 1437648, 1437648, 1437647, 1437648 ],
      "physMemKbytes" : [ 26607, 79823, 133038, 186253, 239469, 291949, 312598, 316277, 317760, 316265, 314752, 313761 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14310,
        "virtualMemoryUsage" : 1472151552,
        "physicalMemoryUsage" : 321114112,
        "heapUsage" : 738000896
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164128,
    "taskID" : "task_1369942127770_1206_m_000040",
    "taskType" : "MAP",
    "finishTime" : 1371222208051,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2115.smile.com" ]
      },
      "hostName" : "/default-rack/a2115.smile.com",
      "startTime" : 1371222187226,
      "result" : "SUCCESS",
      "finishTime" : 1371222208051,
      "attemptID" : "attempt_1369942127770_1206_m_000040_0",
      "clockSplits" : [ 2708, 2709, 2709, 2708, 1324, 1241, 1170, 983, 983, 3572, 201, 201 ],
      "cpuUsages" : [ 1449, 1450, 1449, 1450, 1163, 1146, 1099, 972, 971, 970, 945, 946 ],
      "vmemKbytes" : [ 173689, 521067, 868445, 1215823, 1408585, 1409139, 1409140, 1409139, 1409140, 1409140, 1409139, 1409140 ],
      "physMemKbytes" : [ 37436, 112308, 187180, 262052, 304948, 308090, 311012, 311933, 312102, 312258, 310764, 308111 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14260,
        "virtualMemoryUsage" : 1442959360,
        "physicalMemoryUsage" : 314261504,
        "heapUsage" : 739639296
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164128,
    "taskID" : "task_1369942127770_1206_m_000041",
    "taskType" : "MAP",
    "finishTime" : 1371222209764,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2117.smile.com" ]
      },
      "hostName" : "/default-rack/a2117.smile.com",
      "startTime" : 1371222187225,
      "result" : "SUCCESS",
      "finishTime" : 1371222209764,
      "attemptID" : "attempt_1369942127770_1206_m_000041_0",
      "clockSplits" : [ 2460, 2461, 2461, 2366, 1711, 1637, 1437, 1554, 2430, 3338, 136, 137 ],
      "cpuUsages" : [ 1422, 1423, 1423, 1400, 1245, 1214, 1134, 1139, 1181, 1020, 984, 985 ],
      "vmemKbytes" : [ 183430, 550292, 917154, 1281033, 1420664, 1420663, 1420664, 1420663, 1420664, 1420664, 1420663, 1420664 ],
      "physMemKbytes" : [ 38854, 116562, 194270, 271356, 301598, 302728, 304891, 307428, 308423, 307956, 306560, 305139 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14710,
        "virtualMemoryUsage" : 1454759936,
        "physicalMemoryUsage" : 311668736,
        "heapUsage" : 734986240
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72236,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222164129,
    "taskID" : "task_1369942127770_1206_m_000042",
    "taskType" : "MAP",
    "finishTime" : 1371222210013,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2118.smile.com" ]
      },
      "hostName" : "/default-rack/a2118.smile.com",
      "startTime" : 1371222188236,
      "result" : "SUCCESS",
      "finishTime" : 1371222210013,
      "attemptID" : "attempt_1369942127770_1206_m_000042_0",
      "clockSplits" : [ 2345, 2345, 2346, 2345, 2345, 1441, 1165, 1139, 942, 942, 942, 3297 ],
      "cpuUsages" : [ 1329, 1330, 1330, 1330, 1330, 1110, 1042, 1028, 918, 919, 918, 1176 ],
      "vmemKbytes" : [ 135997, 407991, 679985, 951978, 1223973, 1416016, 1423416, 1423415, 1423416, 1423416, 1423415, 1423416 ],
      "physMemKbytes" : [ 29954, 89862, 149770, 209678, 269587, 312158, 314689, 315603, 315555, 314887, 314219, 313779 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13870,
        "virtualMemoryUsage" : 1457577984,
        "physicalMemoryUsage" : 321241088,
        "heapUsage" : 745275392
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164129,
    "taskID" : "task_1369942127770_1206_m_000043",
    "taskType" : "MAP",
    "finishTime" : 1371222209829,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2117.smile.com" ]
      },
      "hostName" : "/default-rack/a2117.smile.com",
      "startTime" : 1371222188247,
      "result" : "SUCCESS",
      "finishTime" : 1371222209829,
      "attemptID" : "attempt_1369942127770_1206_m_000043_0",
      "clockSplits" : [ 2418, 2419, 2419, 2159, 1443, 1453, 1585, 1514, 988, 987, 988, 3120 ],
      "cpuUsages" : [ 1496, 1497, 1497, 1419, 1200, 1197, 1139, 1120, 984, 983, 984, 1064 ],
      "vmemKbytes" : [ 192713, 578139, 963566, 1335372, 1439240, 1439239, 1439240, 1439239, 1439240, 1439240, 1439239, 1439240 ],
      "physMemKbytes" : [ 41062, 123187, 205312, 284536, 306688, 306724, 308963, 312869, 313477, 311955, 310433, 309628 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14730,
        "virtualMemoryUsage" : 1473781760,
        "physicalMemoryUsage" : 316993536,
        "heapUsage" : 742588416
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164129,
    "taskID" : "task_1369942127770_1206_m_000044",
    "taskType" : "MAP",
    "finishTime" : 1371222205147,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2115.smile.com" ]
      },
      "hostName" : "/default-rack/a2115.smile.com",
      "startTime" : 1371222188243,
      "result" : "SUCCESS",
      "finishTime" : 1371222205147,
      "attemptID" : "attempt_1369942127770_1206_m_000044_0",
      "clockSplits" : [ 1997, 1998, 1998, 1998, 1656, 1015, 1015, 992, 821, 821, 821, 1763 ],
      "cpuUsages" : [ 1362, 1363, 1363, 1363, 1290, 1156, 1155, 1116, 828, 828, 828, 1418 ],
      "vmemKbytes" : [ 154789, 464368, 773947, 1083526, 1374102, 1436109, 1431198, 1426321, 1424416, 1424416, 1424415, 1424416 ],
      "physMemKbytes" : [ 33320, 99960, 166601, 233241, 295602, 306484, 302294, 298121, 295429, 293687, 291944, 290254 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14070,
        "virtualMemoryUsage" : 1458601984,
        "physicalMemoryUsage" : 296701952,
        "heapUsage" : 740294656
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72236,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222164130,
    "taskID" : "task_1369942127770_1206_m_000045",
    "taskType" : "MAP",
    "finishTime" : 1371222208888,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2115.smile.com" ]
      },
      "hostName" : "/default-rack/a2115.smile.com",
      "startTime" : 1371222188284,
      "result" : "SUCCESS",
      "finishTime" : 1371222208888,
      "attemptID" : "attempt_1369942127770_1206_m_000045_0",
      "clockSplits" : [ 2408, 2408, 2409, 2408, 1248, 994, 993, 1546, 1811, 1173, 1142, 2044 ],
      "cpuUsages" : [ 1442, 1443, 1443, 1443, 1216, 1167, 1167, 1242, 1278, 871, 851, 1187 ],
      "vmemKbytes" : [ 170497, 511493, 852489, 1193484, 1419688, 1425179, 1425180, 1425179, 1425180, 1425180, 1425179, 1425180 ],
      "physMemKbytes" : [ 36659, 109977, 183295, 256613, 305816, 308651, 310334, 311695, 312050, 312197, 312184, 312178 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14750,
        "virtualMemoryUsage" : 1459384320,
        "physicalMemoryUsage" : 319713280,
        "heapUsage" : 733020160
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164130,
    "taskID" : "task_1369942127770_1206_m_000046",
    "taskType" : "MAP",
    "finishTime" : 1371222209764,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2115.smile.com" ]
      },
      "hostName" : "/default-rack/a2115.smile.com",
      "startTime" : 1371222188246,
      "result" : "SUCCESS",
      "finishTime" : 1371222209764,
      "attemptID" : "attempt_1369942127770_1206_m_000046_0",
      "clockSplits" : [ 2411, 2412, 2411, 2412, 1354, 1182, 1201, 1287, 1288, 1246, 1233, 3075 ],
      "cpuUsages" : [ 1429, 1430, 1430, 1430, 1204, 1167, 1149, 1069, 1069, 932, 888, 1233 ],
      "vmemKbytes" : [ 170204, 510614, 851024, 1191433, 1405939, 1409271, 1409272, 1409271, 1409272, 1409272, 1409271, 1409272 ],
      "physMemKbytes" : [ 36770, 110311, 183853, 257394, 302847, 301188, 298826, 298087, 298200, 297470, 294709, 291944 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14430,
        "virtualMemoryUsage" : 1443094528,
        "physicalMemoryUsage" : 298102784,
        "heapUsage" : 740622336
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164130,
    "taskID" : "task_1369942127770_1206_m_000047",
    "taskType" : "MAP",
    "finishTime" : 1371222209770,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2115.smile.com" ]
      },
      "hostName" : "/default-rack/a2115.smile.com",
      "startTime" : 1371222188244,
      "result" : "SUCCESS",
      "finishTime" : 1371222209770,
      "attemptID" : "attempt_1369942127770_1206_m_000047_0",
      "clockSplits" : [ 2359, 2360, 2360, 2360, 991, 967, 967, 1864, 1869, 1009, 1009, 3406 ],
      "cpuUsages" : [ 1381, 1382, 1381, 1382, 1111, 1107, 1107, 1332, 1274, 926, 926, 1181 ],
      "vmemKbytes" : [ 175422, 526266, 877111, 1227955, 1409352, 1409403, 1409404, 1409403, 1409404, 1409404, 1409403, 1409404 ],
      "physMemKbytes" : [ 38481, 115443, 192406, 269368, 308485, 307099, 305702, 305330, 307856, 308596, 308675, 308751 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14490,
        "virtualMemoryUsage" : 1443229696,
        "physicalMemoryUsage" : 316166144,
        "heapUsage" : 743505920
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72236,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222164130,
    "taskID" : "task_1369942127770_1206_m_000048",
    "taskType" : "MAP",
    "finishTime" : 1371222209758,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2116.smile.com" ]
      },
      "hostName" : "/default-rack/a2116.smile.com",
      "startTime" : 1371222188248,
      "result" : "SUCCESS",
      "finishTime" : 1371222209758,
      "attemptID" : "attempt_1369942127770_1206_m_000048_0",
      "clockSplits" : [ 2073, 2074, 2074, 2074, 2073, 1639, 1272, 1276, 1301, 1301, 1674, 1842 ],
      "cpuUsages" : [ 1234, 1235, 1235, 1235, 1235, 1183, 1139, 1135, 1103, 1104, 909, 823 ],
      "vmemKbytes" : [ 129783, 389349, 648915, 908480, 1168047, 1389454, 1416722, 1416815, 1416852, 1416852, 1416851, 1416852 ],
      "physMemKbytes" : [ 25359, 76080, 126801, 177521, 228242, 271580, 277354, 277862, 277694, 277090, 276628, 276597 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13990,
        "virtualMemoryUsage" : 1450856448,
        "physicalMemoryUsage" : 287096832,
        "heapUsage" : 598081536
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164131,
    "taskID" : "task_1369942127770_1206_m_000049",
    "taskType" : "MAP",
    "finishTime" : 1371222209877,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2118.smile.com" ]
      },
      "hostName" : "/default-rack/a2118.smile.com",
      "startTime" : 1371222188245,
      "result" : "SUCCESS",
      "finishTime" : 1371222209877,
      "attemptID" : "attempt_1369942127770_1206_m_000049_0",
      "clockSplits" : [ 2394, 2395, 2394, 2395, 2395, 1860, 1150, 1150, 985, 905, 904, 2700 ],
      "cpuUsages" : [ 1252, 1253, 1253, 1253, 1253, 1165, 1047, 1048, 897, 822, 823, 1324 ],
      "vmemKbytes" : [ 126489, 379468, 632447, 885426, 1138405, 1368134, 1409448, 1409495, 1409533, 1409536, 1409535, 1409536 ],
      "physMemKbytes" : [ 26096, 78289, 130482, 182675, 234868, 282278, 290926, 291083, 290759, 288885, 286894, 284958 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13390,
        "virtualMemoryUsage" : 1443364864,
        "physicalMemoryUsage" : 291172352,
        "heapUsage" : 728629248
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164131,
    "taskID" : "task_1369942127770_1206_m_000050",
    "taskType" : "MAP",
    "finishTime" : 1371222209871,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2118.smile.com" ]
      },
      "hostName" : "/default-rack/a2118.smile.com",
      "startTime" : 1371222188246,
      "result" : "SUCCESS",
      "finishTime" : 1371222209871,
      "attemptID" : "attempt_1369942127770_1206_m_000050_0",
      "clockSplits" : [ 2330, 2330, 2331, 2330, 2331, 1760, 1284, 1228, 809, 809, 809, 3269 ],
      "cpuUsages" : [ 1299, 1300, 1300, 1299, 1300, 1110, 952, 941, 864, 865, 864, 1296 ],
      "vmemKbytes" : [ 129180, 387540, 645900, 904260, 1162621, 1382642, 1409412, 1409411, 1409412, 1409412, 1409411, 1409412 ],
      "physMemKbytes" : [ 27390, 82173, 136956, 191739, 246522, 293591, 301780, 304563, 305591, 305485, 305379, 305268 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13390,
        "virtualMemoryUsage" : 1443237888,
        "physicalMemoryUsage" : 312508416,
        "heapUsage" : 745275392
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72236,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222164131,
    "taskID" : "task_1369942127770_1206_m_000051",
    "taskType" : "MAP",
    "finishTime" : 1371222209798,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2117.smile.com" ]
      },
      "hostName" : "/default-rack/a2117.smile.com",
      "startTime" : 1371222189249,
      "result" : "SUCCESS",
      "finishTime" : 1371222209798,
      "attemptID" : "attempt_1369942127770_1206_m_000051_0",
      "clockSplits" : [ 2459, 2460, 2460, 2460, 2460, 1450, 922, 923, 947, 1056, 1056, 1891 ],
      "cpuUsages" : [ 1238, 1239, 1239, 1239, 1239, 1071, 982, 982, 962, 870, 869, 1390 ],
      "vmemKbytes" : [ 133415, 400246, 667077, 933908, 1200739, 1410001, 1425708, 1425707, 1425708, 1425708, 1425707, 1425708 ],
      "physMemKbytes" : [ 29016, 87050, 145083, 203116, 261151, 306685, 310195, 310295, 310356, 308899, 306668, 304504 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13320,
        "virtualMemoryUsage" : 1459924992,
        "physicalMemoryUsage" : 311140352,
        "heapUsage" : 723582976
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164132,
    "taskID" : "task_1369942127770_1206_m_000052",
    "taskType" : "MAP",
    "finishTime" : 1371222209546,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2116.smile.com" ]
      },
      "hostName" : "/default-rack/a2116.smile.com",
      "startTime" : 1371222189252,
      "result" : "SUCCESS",
      "finishTime" : 1371222209546,
      "attemptID" : "attempt_1369942127770_1206_m_000052_0",
      "clockSplits" : [ 2113, 2113, 2113, 2113, 1296, 902, 902, 1007, 1429, 1434, 1749, 3118 ],
      "cpuUsages" : [ 1343, 1343, 1343, 1343, 1163, 1076, 1077, 1044, 916, 918, 1010, 1614 ],
      "vmemKbytes" : [ 165998, 497994, 829990, 1161986, 1418463, 1436051, 1436052, 1435986, 1433726, 1430398, 1428787, 1428788 ],
      "physMemKbytes" : [ 31862, 95586, 159310, 223034, 272810, 278461, 280865, 283418, 290959, 300928, 308895, 314795 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14190,
        "virtualMemoryUsage" : 1463078912,
        "physicalMemoryUsage" : 324112384,
        "heapUsage" : 672333824
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164132,
    "taskID" : "task_1369942127770_1206_m_000053",
    "taskType" : "MAP",
    "finishTime" : 1371222211732,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2117.smile.com" ]
      },
      "hostName" : "/default-rack/a2117.smile.com",
      "startTime" : 1371222189248,
      "result" : "SUCCESS",
      "finishTime" : 1371222211732,
      "attemptID" : "attempt_1369942127770_1206_m_000053_0",
      "clockSplits" : [ 3379, 3380, 3379, 3380, 1214, 1180, 1480, 2139, 2348, 200, 200, 200 ],
      "cpuUsages" : [ 1382, 1382, 1382, 1382, 1146, 1141, 1199, 1326, 847, 768, 767, 768 ],
      "vmemKbytes" : [ 176837, 530511, 884186, 1237861, 1420070, 1420111, 1420112, 1420111, 1420112, 1420112, 1420111, 1420112 ],
      "physMemKbytes" : [ 37701, 113105, 188508, 263911, 302843, 303027, 303274, 304566, 304973, 303294, 301579, 299865 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13490,
        "virtualMemoryUsage" : 1454194688,
        "physicalMemoryUsage" : 306184192,
        "heapUsage" : 723648512
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72236,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222164132,
    "taskID" : "task_1369942127770_1206_m_000054",
    "taskType" : "MAP",
    "finishTime" : 1371222209794,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2115.smile.com" ]
      },
      "hostName" : "/default-rack/a2115.smile.com",
      "startTime" : 1371222189250,
      "result" : "SUCCESS",
      "finishTime" : 1371222209794,
      "attemptID" : "attempt_1369942127770_1206_m_000054_0",
      "clockSplits" : [ 1915, 1915, 1915, 1915, 1915, 1152, 1100, 1150, 1523, 1535, 1698, 2805 ],
      "cpuUsages" : [ 1363, 1363, 1363, 1363, 1363, 1198, 1188, 1163, 976, 977, 997, 1316 ],
      "vmemKbytes" : [ 140637, 421911, 703185, 984458, 1265733, 1423312, 1423856, 1423855, 1423856, 1423856, 1423855, 1423856 ],
      "physMemKbytes" : [ 30869, 92607, 154346, 216085, 277824, 313467, 315980, 318362, 319339, 319401, 317453, 314073 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14630,
        "virtualMemoryUsage" : 1458028544,
        "physicalMemoryUsage" : 320655360,
        "heapUsage" : 740294656
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164133,
    "taskID" : "task_1369942127770_1206_m_000055",
    "taskType" : "MAP",
    "finishTime" : 1371222209829,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2118.smile.com" ]
      },
      "hostName" : "/default-rack/a2118.smile.com",
      "startTime" : 1371222190266,
      "result" : "SUCCESS",
      "finishTime" : 1371222209829,
      "attemptID" : "attempt_1369942127770_1206_m_000055_0",
      "clockSplits" : [ 2156, 2157, 2157, 2157, 1560, 923, 923, 951, 1166, 1165, 3553, 294 ],
      "cpuUsages" : [ 1567, 1568, 1568, 1568, 1331, 1078, 1079, 1081, 1094, 1094, 1067, 1035 ],
      "vmemKbytes" : [ 159573, 478720, 797867, 1117014, 1398782, 1441271, 1441272, 1441271, 1441272, 1441272, 1441271, 1441272 ],
      "physMemKbytes" : [ 34072, 102218, 170364, 238509, 298798, 308785, 309842, 310878, 310220, 308476, 306900, 306628 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 15280,
        "virtualMemoryUsage" : 1475862528,
        "physicalMemoryUsage" : 313933824,
        "heapUsage" : 740491264
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164133,
    "taskID" : "task_1369942127770_1206_m_000056",
    "taskType" : "MAP",
    "finishTime" : 1371222209766,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2116.smile.com" ]
      },
      "hostName" : "/default-rack/a2116.smile.com",
      "startTime" : 1371222190265,
      "result" : "SUCCESS",
      "finishTime" : 1371222209766,
      "attemptID" : "attempt_1369942127770_1206_m_000056_0",
      "clockSplits" : [ 1671, 1672, 1672, 1672, 1671, 1485, 1304, 1382, 1955, 1637, 1335, 2040 ],
      "cpuUsages" : [ 1307, 1307, 1308, 1307, 1307, 1228, 1152, 1137, 1033, 976, 924, 1214 ],
      "vmemKbytes" : [ 129345, 388038, 646731, 905424, 1164117, 1389407, 1420696, 1420695, 1420696, 1420696, 1420695, 1420696 ],
      "physMemKbytes" : [ 29283, 87851, 146419, 204986, 263554, 314885, 324186, 326689, 327672, 327261, 324457, 321292 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14200,
        "virtualMemoryUsage" : 1454792704,
        "physicalMemoryUsage" : 327684096,
        "heapUsage" : 659947520
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72236,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222164133,
    "taskID" : "task_1369942127770_1206_m_000057",
    "taskType" : "MAP",
    "finishTime" : 1371222209780,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2115.smile.com" ]
      },
      "hostName" : "/default-rack/a2115.smile.com",
      "startTime" : 1371222190273,
      "result" : "SUCCESS",
      "finishTime" : 1371222209780,
      "attemptID" : "attempt_1369942127770_1206_m_000057_0",
      "clockSplits" : [ 1937, 1937, 1937, 1937, 1937, 1808, 921, 921, 920, 1160, 1247, 2840 ],
      "cpuUsages" : [ 1261, 1262, 1262, 1261, 1262, 1229, 1008, 1008, 1007, 884, 839, 1247 ],
      "vmemKbytes" : [ 120936, 362811, 604686, 846560, 1088435, 1328359, 1420532, 1420531, 1420532, 1420532, 1420531, 1420532 ],
      "physMemKbytes" : [ 26360, 79080, 131800, 184520, 237241, 289556, 311113, 313482, 315853, 316764, 313891, 310918 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13530,
        "virtualMemoryUsage" : 1454624768,
        "physicalMemoryUsage" : 317476864,
        "heapUsage" : 728170496
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164133,
    "taskID" : "task_1369942127770_1206_m_000058",
    "taskType" : "MAP",
    "finishTime" : 1371222209289,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2115.smile.com" ]
      },
      "hostName" : "/default-rack/a2115.smile.com",
      "startTime" : 1371222190266,
      "result" : "SUCCESS",
      "finishTime" : 1371222209289,
      "attemptID" : "attempt_1369942127770_1206_m_000058_0",
      "clockSplits" : [ 1567, 1568, 1568, 1568, 1568, 1568, 1852, 2691, 800, 800, 799, 2669 ],
      "cpuUsages" : [ 1236, 1236, 1237, 1236, 1237, 1236, 1292, 1435, 895, 895, 895, 1280 ],
      "vmemKbytes" : [ 104429, 313288, 522147, 731006, 939866, 1148725, 1353137, 1418911, 1418912, 1418912, 1418911, 1418912 ],
      "physMemKbytes" : [ 22528, 67586, 112643, 157700, 202758, 247815, 292006, 309112, 309629, 307720, 305811, 303966 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14110,
        "virtualMemoryUsage" : 1452965888,
        "physicalMemoryUsage" : 310743040,
        "heapUsage" : 740360192
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164134,
    "taskID" : "task_1369942127770_1206_m_000059",
    "taskType" : "MAP",
    "finishTime" : 1371222211562,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2117.smile.com" ]
      },
      "hostName" : "/default-rack/a2117.smile.com",
      "startTime" : 1371222192248,
      "result" : "SUCCESS",
      "finishTime" : 1371222211562,
      "attemptID" : "attempt_1369942127770_1206_m_000059_0",
      "clockSplits" : [ 2767, 2767, 2768, 2767, 849, 819, 819, 967, 2079, 2282, 212, 213 ],
      "cpuUsages" : [ 1464, 1464, 1465, 1464, 1054, 1047, 1047, 1043, 1010, 905, 843, 844 ],
      "vmemKbytes" : [ 177532, 532598, 887664, 1242729, 1425654, 1425695, 1425696, 1425695, 1425696, 1425696, 1425695, 1425696 ],
      "physMemKbytes" : [ 35793, 107380, 178967, 250554, 288480, 290645, 292802, 294945, 295913, 295599, 293360, 290944 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13650,
        "virtualMemoryUsage" : 1459912704,
        "physicalMemoryUsage" : 296689664,
        "heapUsage" : 740425728
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72236,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222164134,
    "taskID" : "task_1369942127770_1206_m_000060",
    "taskType" : "MAP",
    "finishTime" : 1371222225441,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2118.smile.com" ]
      },
      "hostName" : "/default-rack/a2118.smile.com",
      "startTime" : 1371222206294,
      "result" : "SUCCESS",
      "finishTime" : 1371222225441,
      "attemptID" : "attempt_1369942127770_1206_m_000060_0",
      "clockSplits" : [ 1420, 1420, 1421, 1420, 1420, 1421, 1382, 1130, 1130, 1252, 1448, 4180 ],
      "cpuUsages" : [ 1221, 1222, 1222, 1222, 1221, 1222, 1208, 1119, 1120, 1000, 808, 1165 ],
      "vmemKbytes" : [ 104942, 314826, 524711, 734595, 944480, 1154364, 1362397, 1441311, 1441312, 1441312, 1441311, 1441312 ],
      "physMemKbytes" : [ 22576, 67728, 112881, 158034, 203187, 248339, 293119, 311828, 314605, 317000, 315530, 313123 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13850,
        "virtualMemoryUsage" : 1475903488,
        "physicalMemoryUsage" : 319762432,
        "heapUsage" : 742326272
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164134,
    "taskID" : "task_1369942127770_1206_m_000061",
    "taskType" : "MAP",
    "finishTime" : 1371222221672,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2116.smile.com" ]
      },
      "hostName" : "/default-rack/a2116.smile.com",
      "startTime" : 1371222206296,
      "result" : "SUCCESS",
      "finishTime" : 1371222221672,
      "attemptID" : "attempt_1369942127770_1206_m_000061_0",
      "clockSplits" : [ 1217, 1218, 1218, 1218, 1218, 1217, 1218, 1218, 1106, 972, 972, 2580 ],
      "cpuUsages" : [ 1102, 1102, 1103, 1102, 1103, 1102, 1102, 1103, 1008, 896, 896, 1281 ],
      "vmemKbytes" : [ 84070, 252211, 420352, 588493, 756634, 924775, 1092916, 1261057, 1411777, 1436728, 1436727, 1436728 ],
      "physMemKbytes" : [ 16339, 49018, 81697, 114376, 147055, 179734, 212413, 245092, 274523, 280502, 281827, 283091 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 12900,
        "virtualMemoryUsage" : 1471209472,
        "physicalMemoryUsage" : 290119680,
        "heapUsage" : 674496512
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164135,
    "taskID" : "task_1369942127770_1206_m_000062",
    "taskType" : "MAP",
    "finishTime" : 1371222221958,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2115.smile.com" ]
      },
      "hostName" : "/default-rack/a2115.smile.com",
      "startTime" : 1371222206298,
      "result" : "SUCCESS",
      "finishTime" : 1371222221958,
      "attemptID" : "attempt_1369942127770_1206_m_000062_0",
      "clockSplits" : [ 1276, 1276, 1277, 1276, 1276, 1277, 1253, 882, 881, 882, 3492, 94 ],
      "cpuUsages" : [ 1156, 1157, 1157, 1157, 1157, 1156, 1148, 990, 990, 990, 1022, 1060 ],
      "vmemKbytes" : [ 101522, 304566, 507610, 710654, 913699, 1116743, 1319439, 1409411, 1409412, 1409412, 1409411, 1409412 ],
      "physMemKbytes" : [ 21852, 65556, 109261, 152965, 196670, 240375, 284000, 301612, 298461, 295309, 292461, 291917 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13490,
        "virtualMemoryUsage" : 1443237888,
        "physicalMemoryUsage" : 299003904,
        "heapUsage" : 723582976
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72236,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222164135,
    "taskID" : "task_1369942127770_1206_m_000063",
    "taskType" : "MAP",
    "finishTime" : 1371222227163,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2118.smile.com" ]
      },
      "hostName" : "/default-rack/a2118.smile.com",
      "startTime" : 1371222207290,
      "result" : "SUCCESS",
      "finishTime" : 1371222227163,
      "attemptID" : "attempt_1369942127770_1206_m_000063_0",
      "clockSplits" : [ 1693, 1694, 1694, 1694, 1694, 1694, 1713, 2362, 4563, 269, 269, 270 ],
      "cpuUsages" : [ 1238, 1239, 1238, 1239, 1238, 1239, 1171, 1204, 1078, 895, 895, 896 ],
      "vmemKbytes" : [ 117528, 352587, 587645, 822702, 1057761, 1292818, 1424850, 1425327, 1425328, 1425328, 1425327, 1425328 ],
      "physMemKbytes" : [ 25760, 77280, 128800, 180319, 231840, 283359, 312333, 312567, 315051, 313807, 312106, 310406 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13690,
        "virtualMemoryUsage" : 1459535872,
        "physicalMemoryUsage" : 316899328,
        "heapUsage" : 735051776
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164135,
    "taskID" : "task_1369942127770_1206_m_000064",
    "taskType" : "MAP",
    "finishTime" : 1371222227254,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2118.smile.com" ]
      },
      "hostName" : "/default-rack/a2118.smile.com",
      "startTime" : 1371222207289,
      "result" : "SUCCESS",
      "finishTime" : 1371222227254,
      "attemptID" : "attempt_1369942127770_1206_m_000064_0",
      "clockSplits" : [ 1705, 1705, 1705, 1705, 1705, 1702, 1632, 1534, 805, 806, 806, 3859 ],
      "cpuUsages" : [ 1228, 1228, 1228, 1228, 1228, 1225, 1140, 1106, 851, 851, 851, 1326 ],
      "vmemKbytes" : [ 119109, 357327, 595545, 833763, 1071982, 1310012, 1419836, 1419835, 1419836, 1419836, 1419835, 1419836 ],
      "physMemKbytes" : [ 25719, 77160, 128601, 180042, 231483, 282884, 306601, 306586, 305168, 302849, 300530, 298267 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13590,
        "virtualMemoryUsage" : 1453912064,
        "physicalMemoryUsage" : 304549888,
        "heapUsage" : 734855168
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164135,
    "taskID" : "task_1369942127770_1206_m_000065",
    "taskType" : "MAP",
    "finishTime" : 1371222230890,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2118.smile.com" ]
      },
      "hostName" : "/default-rack/a2118.smile.com",
      "startTime" : 1371222207289,
      "result" : "SUCCESS",
      "finishTime" : 1371222230890,
      "attemptID" : "attempt_1369942127770_1206_m_000065_0",
      "clockSplits" : [ 2510, 2511, 2511, 2511, 881, 855, 855, 3817, 956, 957, 957, 4275 ],
      "cpuUsages" : [ 1419, 1420, 1419, 1420, 975, 968, 969, 1185, 898, 899, 898, 1340 ],
      "vmemKbytes" : [ 175498, 526495, 877492, 1228489, 1409318, 1409359, 1409360, 1409359, 1409360, 1409360, 1409359, 1409360 ],
      "physMemKbytes" : [ 37886, 113658, 189430, 265201, 304989, 306545, 308094, 310685, 313574, 311030, 308486, 306866 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13810,
        "virtualMemoryUsage" : 1443184640,
        "physicalMemoryUsage" : 314040320,
        "heapUsage" : 734920704
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72236,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222164136,
    "taskID" : "task_1369942127770_1206_m_000066",
    "taskType" : "MAP",
    "finishTime" : 1371222241231,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2115.smile.com" ]
      },
      "hostName" : "/default-rack/a2115.smile.com",
      "startTime" : 1371222209293,
      "result" : "SUCCESS",
      "finishTime" : 1371222241231,
      "attemptID" : "attempt_1369942127770_1206_m_000066_0",
      "clockSplits" : [ 1787, 1787, 1788, 1787, 1787, 1088, 1085, 4240, 1584, 1583, 1583, 11834 ],
      "cpuUsages" : [ 1420, 1421, 1421, 1421, 1421, 1182, 1181, 1242, 848, 848, 848, 1147 ],
      "vmemKbytes" : [ 141716, 425148, 708581, 992014, 1275447, 1418363, 1418476, 1418585, 1418628, 1418628, 1418627, 1418628 ],
      "physMemKbytes" : [ 30409, 91229, 152048, 212867, 273687, 305868, 308943, 311994, 312816, 312180, 311543, 310922 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14400,
        "virtualMemoryUsage" : 1452675072,
        "physicalMemoryUsage" : 318169088,
        "heapUsage" : 745537536
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164136,
    "taskID" : "task_1369942127770_1206_m_000067",
    "taskType" : "MAP",
    "finishTime" : 1371222225138,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2116.smile.com" ]
      },
      "hostName" : "/default-rack/a2116.smile.com",
      "startTime" : 1371222209294,
      "result" : "SUCCESS",
      "finishTime" : 1371222225138,
      "attemptID" : "attempt_1369942127770_1206_m_000067_0",
      "clockSplits" : [ 1165, 1166, 1165, 1166, 1166, 1165, 1166, 928, 723, 722, 722, 3621 ],
      "cpuUsages" : [ 1214, 1215, 1215, 1215, 1215, 1215, 1215, 1030, 869, 869, 869, 1129 ],
      "vmemKbytes" : [ 95135, 285405, 475675, 665945, 856216, 1046485, 1236756, 1399842, 1420452, 1420452, 1420451, 1420452 ],
      "physMemKbytes" : [ 18348, 55044, 91740, 128435, 165132, 201827, 238524, 271054, 281752, 289291, 296830, 304922 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13660,
        "virtualMemoryUsage" : 1454542848,
        "physicalMemoryUsage" : 319750144,
        "heapUsage" : 673841152
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164136,
    "taskID" : "task_1369942127770_1206_m_000068",
    "taskType" : "MAP",
    "finishTime" : 1371222230905,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2115.smile.com" ]
      },
      "hostName" : "/default-rack/a2115.smile.com",
      "startTime" : 1371222210298,
      "result" : "SUCCESS",
      "finishTime" : 1371222230905,
      "attemptID" : "attempt_1369942127770_1206_m_000068_0",
      "clockSplits" : [ 2408, 2408, 2408, 2409, 1930, 1718, 1379, 1310, 798, 798, 797, 2239 ],
      "cpuUsages" : [ 1541, 1542, 1541, 1542, 1162, 1109, 1032, 997, 741, 740, 740, 1213 ],
      "vmemKbytes" : [ 178979, 536937, 894896, 1252854, 1437270, 1437311, 1437312, 1437311, 1437312, 1437312, 1437311, 1437312 ],
      "physMemKbytes" : [ 38258, 114775, 191292, 267809, 307294, 307439, 307617, 307800, 307382, 306576, 305769, 304983 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13900,
        "virtualMemoryUsage" : 1471807488,
        "physicalMemoryUsage" : 312037376,
        "heapUsage" : 723648512
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72236,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222164137,
    "taskID" : "task_1369942127770_1206_m_000069",
    "taskType" : "MAP",
    "finishTime" : 1371222225328,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2116.smile.com" ]
      },
      "hostName" : "/default-rack/a2116.smile.com",
      "startTime" : 1371222210302,
      "result" : "SUCCESS",
      "finishTime" : 1371222225328,
      "attemptID" : "attempt_1369942127770_1206_m_000069_0",
      "clockSplits" : [ 1091, 1092, 1091, 1092, 1092, 1091, 1092, 1058, 803, 804, 804, 3736 ],
      "cpuUsages" : [ 1107, 1107, 1108, 1107, 1108, 1107, 1108, 1073, 810, 810, 810, 1305 ],
      "vmemKbytes" : [ 89984, 269954, 449923, 629892, 809862, 989831, 1169801, 1348531, 1418636, 1418636, 1418635, 1418636 ],
      "physMemKbytes" : [ 17538, 52614, 87690, 122765, 157842, 192917, 227994, 262894, 282408, 291993, 301578, 311953 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 12740,
        "virtualMemoryUsage" : 1452683264,
        "physicalMemoryUsage" : 329846784,
        "heapUsage" : 655818752
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164137,
    "taskID" : "task_1369942127770_1206_m_000070",
    "taskType" : "MAP",
    "finishTime" : 1371222230923,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2116.smile.com" ]
      },
      "hostName" : "/default-rack/a2116.smile.com",
      "startTime" : 1371222210301,
      "result" : "SUCCESS",
      "finishTime" : 1371222230923,
      "attemptID" : "attempt_1369942127770_1206_m_000070_0",
      "clockSplits" : [ 1667, 1667, 1667, 1667, 1667, 1451, 1272, 1327, 1731, 1653, 1491, 3356 ],
      "cpuUsages" : [ 1331, 1331, 1331, 1331, 1332, 1146, 993, 987, 942, 944, 946, 1406 ],
      "vmemKbytes" : [ 130716, 392148, 653580, 915011, 1176444, 1398858, 1425760, 1425759, 1425760, 1425760, 1425759, 1425760 ],
      "physMemKbytes" : [ 25209, 75627, 126045, 176463, 226882, 269981, 276408, 277936, 291940, 312986, 321089, 324970 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 14020,
        "virtualMemoryUsage" : 1459978240,
        "physicalMemoryUsage" : 333864960,
        "heapUsage" : 675282944
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164137,
    "taskID" : "task_1369942127770_1206_m_000071",
    "taskType" : "MAP",
    "finishTime" : 1371222243147,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2115.smile.com" ]
      },
      "hostName" : "/default-rack/a2115.smile.com",
      "startTime" : 1371222210300,
      "result" : "SUCCESS",
      "finishTime" : 1371222243147,
      "attemptID" : "attempt_1369942127770_1206_m_000071_0",
      "clockSplits" : [ 2317, 2317, 2317, 2318, 830, 804, 803, 20720, 104, 104, 104, 104 ],
      "cpuUsages" : [ 1476, 1476, 1476, 1476, 1068, 1062, 1061, 1367, 764, 765, 764, 765 ],
      "vmemKbytes" : [ 179124, 537372, 895621, 1253870, 1439095, 1439147, 1439148, 1439147, 1439148, 1439148, 1439147, 1439148 ],
      "physMemKbytes" : [ 38656, 115968, 193280, 270591, 311557, 313623, 315680, 317727, 317803, 316372, 314939, 313508 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13520,
        "virtualMemoryUsage" : 1473687552,
        "physicalMemoryUsage" : 320299008,
        "heapUsage" : 740425728
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72236,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222164138,
    "taskID" : "task_1369942127770_1206_m_000072",
    "taskType" : "MAP",
    "finishTime" : 1371222230925,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2118.smile.com" ]
      },
      "hostName" : "/default-rack/a2118.smile.com",
      "startTime" : 1371222210302,
      "result" : "SUCCESS",
      "finishTime" : 1371222230925,
      "attemptID" : "attempt_1369942127770_1206_m_000072_0",
      "clockSplits" : [ 2052, 2053, 2052, 2053, 2052, 1753, 1343, 1286, 863, 863, 863, 3385 ],
      "cpuUsages" : [ 1301, 1302, 1301, 1302, 1301, 1214, 1093, 1070, 887, 887, 887, 1265 ],
      "vmemKbytes" : [ 129050, 387150, 645250, 903350, 1161451, 1396549, 1439636, 1439635, 1439636, 1439636, 1439635, 1439636 ],
      "physMemKbytes" : [ 27936, 83809, 139683, 195556, 251430, 302570, 314193, 316918, 316967, 315288, 313608, 312145 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13810,
        "virtualMemoryUsage" : 1474187264,
        "physicalMemoryUsage" : 319389696,
        "heapUsage" : 725942272
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164138,
    "taskID" : "task_1369942127770_1206_m_000073",
    "taskType" : "MAP",
    "finishTime" : 1371222233643,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2116.smile.com" ]
      },
      "hostName" : "/default-rack/a2116.smile.com",
      "startTime" : 1371222211323,
      "result" : "SUCCESS",
      "finishTime" : 1371222233643,
      "attemptID" : "attempt_1369942127770_1206_m_000073_0",
      "clockSplits" : [ 2850, 2850, 2851, 2850, 3634, 922, 921, 915, 864, 864, 863, 1931 ],
      "cpuUsages" : [ 1422, 1422, 1422, 1422, 1195, 974, 974, 956, 814, 813, 814, 1162 ],
      "vmemKbytes" : [ 178725, 536177, 893628, 1251079, 1430284, 1428495, 1428496, 1428495, 1428496, 1428496, 1428495, 1428496 ],
      "physMemKbytes" : [ 33814, 101442, 169070, 236698, 286736, 296978, 302601, 308176, 309819, 308938, 308057, 307285 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13390,
        "virtualMemoryUsage" : 1462779904,
        "physicalMemoryUsage" : 314519552,
        "heapUsage" : 656801792
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164138,
    "taskID" : "task_1369942127770_1206_m_000074",
    "taskType" : "MAP",
    "finishTime" : 1371222231021,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2115.smile.com" ]
      },
      "hostName" : "/default-rack/a2115.smile.com",
      "startTime" : 1371222211318,
      "result" : "SUCCESS",
      "finishTime" : 1371222231021,
      "attemptID" : "attempt_1369942127770_1206_m_000074_0",
      "clockSplits" : [ 2026, 2027, 2026, 2027, 1805, 998, 998, 1150, 2266, 1405, 1124, 1124 ],
      "cpuUsages" : [ 1354, 1354, 1355, 1354, 1264, 933, 933, 931, 908, 776, 734, 734 ],
      "vmemKbytes" : [ 148613, 445839, 743066, 1040292, 1330604, 1422019, 1422020, 1422019, 1422020, 1422020, 1422019, 1422020 ],
      "physMemKbytes" : [ 32329, 96989, 161649, 226308, 289469, 309488, 309679, 309869, 309968, 309242, 306663, 304002 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13210,
        "virtualMemoryUsage" : 1456148480,
        "physicalMemoryUsage" : 313556992,
        "heapUsage" : 723648512
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72236,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222164139,
    "taskID" : "task_1369942127770_1206_m_000075",
    "taskType" : "MAP",
    "finishTime" : 1371222230806,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2117.smile.com" ]
      },
      "hostName" : "/default-rack/a2117.smile.com",
      "startTime" : 1371222211320,
      "result" : "SUCCESS",
      "finishTime" : 1371222230806,
      "attemptID" : "attempt_1369942127770_1206_m_000075_0",
      "clockSplits" : [ 1609, 1609, 1610, 1609, 1609, 1610, 1609, 1437, 800, 800, 800, 3712 ],
      "cpuUsages" : [ 1126, 1126, 1126, 1126, 1127, 1126, 1126, 1078, 896, 897, 896, 1050 ],
      "vmemKbytes" : [ 91606, 274818, 458031, 641244, 824457, 1007670, 1190883, 1369937, 1426664, 1426664, 1426663, 1426664 ],
      "physMemKbytes" : [ 18989, 56967, 94945, 132922, 170901, 208878, 246857, 284002, 296666, 297978, 299289, 300540 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13070,
        "virtualMemoryUsage" : 1460903936,
        "physicalMemoryUsage" : 308080640,
        "heapUsage" : 728367104
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164139,
    "taskID" : "task_1369942127770_1206_m_000076",
    "taskType" : "MAP",
    "finishTime" : 1371222231013,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2118.smile.com" ]
      },
      "hostName" : "/default-rack/a2118.smile.com",
      "startTime" : 1371222211322,
      "result" : "SUCCESS",
      "finishTime" : 1371222231013,
      "attemptID" : "attempt_1369942127770_1206_m_000076_0",
      "clockSplits" : [ 1510, 1510, 1510, 1510, 1511, 1510, 1543, 3155, 989, 989, 3170, 779 ],
      "cpuUsages" : [ 1182, 1183, 1183, 1183, 1183, 1183, 1182, 1138, 1003, 1002, 1004, 1174 ],
      "vmemKbytes" : [ 103292, 309877, 516463, 723048, 929633, 1136218, 1342774, 1442573, 1441656, 1440154, 1438651, 1437908 ],
      "physMemKbytes" : [ 22609, 67829, 113048, 158267, 203487, 248706, 293920, 315760, 313552, 309910, 306268, 304426 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13600,
        "virtualMemoryUsage" : 1472417792,
        "physicalMemoryUsage" : 311689216,
        "heapUsage" : 723517440
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164139,
    "taskID" : "task_1369942127770_1206_m_000077",
    "taskType" : "MAP",
    "finishTime" : 1371222230986,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2118.smile.com" ]
      },
      "hostName" : "/default-rack/a2118.smile.com",
      "startTime" : 1371222211321,
      "result" : "SUCCESS",
      "finishTime" : 1371222230986,
      "attemptID" : "attempt_1369942127770_1206_m_000077_0",
      "clockSplits" : [ 2453, 2454, 2454, 2454, 983, 819, 818, 834, 953, 953, 952, 3225 ],
      "cpuUsages" : [ 1358, 1358, 1358, 1358, 1081, 1050, 1050, 1036, 929, 930, 930, 882 ],
      "vmemKbytes" : [ 173221, 519663, 866105, 1212547, 1419007, 1420775, 1420776, 1420775, 1420776, 1420776, 1420775, 1420776 ],
      "physMemKbytes" : [ 37672, 113016, 188361, 263705, 309197, 311033, 312493, 313926, 312972, 310477, 307981, 306375 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13630,
        "virtualMemoryUsage" : 1454874624,
        "physicalMemoryUsage" : 313704448,
        "heapUsage" : 723517440
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72236,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222164139,
    "taskID" : "task_1369942127770_1206_m_000078",
    "taskType" : "MAP",
    "finishTime" : 1371222230909,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2118.smile.com" ]
      },
      "hostName" : "/default-rack/a2118.smile.com",
      "startTime" : 1371222211322,
      "result" : "SUCCESS",
      "finishTime" : 1371222230909,
      "attemptID" : "attempt_1369942127770_1206_m_000078_0",
      "clockSplits" : [ 1544, 1545, 1545, 1545, 1545, 1544, 1888, 2074, 1066, 1066, 3565, 400 ],
      "cpuUsages" : [ 1237, 1237, 1238, 1237, 1237, 1238, 1206, 1153, 966, 966, 971, 994 ],
      "vmemKbytes" : [ 109547, 328642, 547738, 766833, 985929, 1205024, 1394686, 1420099, 1420100, 1420100, 1420099, 1420100 ],
      "physMemKbytes" : [ 23842, 71526, 119211, 166896, 214581, 262265, 303541, 309043, 308442, 307473, 306523, 306207 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13790,
        "virtualMemoryUsage" : 1454182400,
        "physicalMemoryUsage" : 313499648,
        "heapUsage" : 734789632
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164140,
    "taskID" : "task_1369942127770_1206_m_000079",
    "taskType" : "MAP",
    "finishTime" : 1371222241634,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2117.smile.com" ]
      },
      "hostName" : "/default-rack/a2117.smile.com",
      "startTime" : 1371222212318,
      "result" : "SUCCESS",
      "finishTime" : 1371222241634,
      "attemptID" : "attempt_1369942127770_1206_m_000079_0",
      "clockSplits" : [ 1728, 1728, 1728, 1728, 1729, 1728, 1984, 16602, 89, 88, 89, 89 ],
      "cpuUsages" : [ 1174, 1174, 1175, 1174, 1175, 1174, 1167, 1241, 766, 767, 766, 767 ],
      "vmemKbytes" : [ 109233, 327701, 546169, 764636, 983104, 1201571, 1396830, 1428571, 1428572, 1428572, 1428571, 1428572 ],
      "physMemKbytes" : [ 23542, 70627, 117712, 164797, 211882, 258967, 301447, 311452, 311774, 309734, 307693, 305652 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 12520,
        "virtualMemoryUsage" : 1462857728,
        "physicalMemoryUsage" : 311943168,
        "heapUsage" : 723714048
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164140,
    "taskID" : "task_1369942127770_1206_m_000080",
    "taskType" : "MAP",
    "finishTime" : 1371222234375,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2117.smile.com" ]
      },
      "hostName" : "/default-rack/a2117.smile.com",
      "startTime" : 1371222212320,
      "result" : "SUCCESS",
      "finishTime" : 1371222234375,
      "attemptID" : "attempt_1369942127770_1206_m_000080_0",
      "clockSplits" : [ 2667, 2668, 2668, 2668, 1137, 1111, 1291, 2903, 866, 867, 866, 2337 ],
      "cpuUsages" : [ 1361, 1362, 1361, 1362, 1011, 1005, 1020, 1148, 925, 926, 925, 1094 ],
      "vmemKbytes" : [ 179085, 537255, 895425, 1253594, 1438779, 1438831, 1438832, 1438831, 1438832, 1438832, 1438831, 1438832 ],
      "physMemKbytes" : [ 38347, 115043, 191739, 268434, 308300, 308750, 309191, 309763, 309488, 308604, 307719, 306908 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13500,
        "virtualMemoryUsage" : 1473363968,
        "physicalMemoryUsage" : 314023936,
        "heapUsage" : 723714048
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72236,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222164140,
    "taskID" : "task_1369942127770_1206_m_000081",
    "taskType" : "MAP",
    "finishTime" : 1371222233436,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2117.smile.com" ]
      },
      "hostName" : "/default-rack/a2117.smile.com",
      "startTime" : 1371222212365,
      "result" : "SUCCESS",
      "finishTime" : 1371222233436,
      "attemptID" : "attempt_1369942127770_1206_m_000081_0",
      "clockSplits" : [ 2872, 2872, 2872, 2872, 1498, 1478, 1590, 1608, 2713, 183, 183, 184 ],
      "cpuUsages" : [ 1372, 1372, 1372, 1373, 1091, 1087, 955, 934, 919, 918, 918, 919 ],
      "vmemKbytes" : [ 177378, 532136, 886894, 1241651, 1424418, 1424459, 1424460, 1424459, 1424460, 1424460, 1424459, 1424460 ],
      "physMemKbytes" : [ 36488, 109466, 182444, 255421, 293123, 293347, 293592, 293884, 293699, 292787, 291867, 290948 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13300,
        "virtualMemoryUsage" : 1458647040,
        "physicalMemoryUsage" : 297373696,
        "heapUsage" : 723648512
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164141,
    "taskID" : "task_1369942127770_1206_m_000082",
    "taskType" : "MAP",
    "finishTime" : 1371222234234,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2117.smile.com" ]
      },
      "hostName" : "/default-rack/a2117.smile.com",
      "startTime" : 1371222212323,
      "result" : "SUCCESS",
      "finishTime" : 1371222234234,
      "attemptID" : "attempt_1369942127770_1206_m_000082_0",
      "clockSplits" : [ 2757, 2758, 2757, 2758, 2290, 984, 985, 1009, 1193, 1192, 2624, 600 ],
      "cpuUsages" : [ 1283, 1284, 1284, 1283, 1212, 1011, 1012, 1009, 997, 997, 992, 986 ],
      "vmemKbytes" : [ 150595, 451787, 752979, 1054170, 1344891, 1426535, 1426536, 1426535, 1426536, 1426536, 1426535, 1426536 ],
      "physMemKbytes" : [ 30285, 90858, 151430, 212001, 270566, 289039, 291857, 294632, 293797, 290645, 287899, 287491 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13350,
        "virtualMemoryUsage" : 1460772864,
        "physicalMemoryUsage" : 294371328,
        "heapUsage" : 723714048
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164141,
    "taskID" : "task_1369942127770_1206_m_000083",
    "taskType" : "MAP",
    "finishTime" : 1371222234374,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2117.smile.com" ]
      },
      "hostName" : "/default-rack/a2117.smile.com",
      "startTime" : 1371222212325,
      "result" : "SUCCESS",
      "finishTime" : 1371222234374,
      "attemptID" : "attempt_1369942127770_1206_m_000083_0",
      "clockSplits" : [ 2632, 2633, 2633, 2632, 2063, 1726, 1324, 1287, 1007, 1006, 1668, 1433 ],
      "cpuUsages" : [ 1382, 1382, 1382, 1382, 1210, 1095, 961, 964, 990, 990, 989, 963 ],
      "vmemKbytes" : [ 179482, 538446, 897411, 1256376, 1441309, 1441351, 1441352, 1441351, 1441352, 1441352, 1441351, 1441352 ],
      "physMemKbytes" : [ 38229, 114688, 191148, 267607, 307615, 309001, 311141, 313407, 313352, 311808, 310265, 309532 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13690,
        "virtualMemoryUsage" : 1475944448,
        "physicalMemoryUsage" : 316928000,
        "heapUsage" : 744685568
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72236,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222164141,
    "taskID" : "task_1369942127770_1206_m_000084",
    "taskType" : "MAP",
    "finishTime" : 1371222233257,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2117.smile.com" ]
      },
      "hostName" : "/default-rack/a2117.smile.com",
      "startTime" : 1371222212365,
      "result" : "SUCCESS",
      "finishTime" : 1371222233257,
      "attemptID" : "attempt_1369942127770_1206_m_000084_0",
      "clockSplits" : [ 3170, 3170, 3170, 3170, 841, 800, 800, 873, 1410, 1411, 1720, 353 ],
      "cpuUsages" : [ 1398, 1399, 1399, 1399, 938, 929, 929, 934, 971, 970, 919, 915 ],
      "vmemKbytes" : [ 176295, 528885, 881475, 1234064, 1416363, 1416415, 1416416, 1416415, 1416416, 1416416, 1416415, 1416416 ],
      "physMemKbytes" : [ 35965, 107895, 179825, 251754, 289004, 289138, 289261, 289383, 289436, 289443, 288025, 284629 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13100,
        "virtualMemoryUsage" : 1450409984,
        "physicalMemoryUsage" : 289714176,
        "heapUsage" : 723582976
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164142,
    "taskID" : "task_1369942127770_1206_m_000085",
    "taskType" : "MAP",
    "finishTime" : 1371222240854,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2116.smile.com" ]
      },
      "hostName" : "/default-rack/a2116.smile.com",
      "startTime" : 1371222212327,
      "result" : "SUCCESS",
      "finishTime" : 1371222240854,
      "attemptID" : "attempt_1369942127770_1206_m_000085_0",
      "clockSplits" : [ 2049, 2050, 2049, 2050, 2046, 1082, 1082, 15663, 112, 113, 113, 113 ],
      "cpuUsages" : [ 1282, 1283, 1283, 1283, 1282, 865, 866, 1015, 778, 777, 778, 778 ],
      "vmemKbytes" : [ 143872, 431618, 719364, 1007109, 1294853, 1437631, 1437632, 1437631, 1437632, 1437632, 1437631, 1437632 ],
      "physMemKbytes" : [ 31953, 95859, 159765, 223671, 287577, 319740, 320639, 321522, 320713, 318771, 316828, 314887 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 12270,
        "virtualMemoryUsage" : 1472135168,
        "physicalMemoryUsage" : 321449984,
        "heapUsage" : 656867328
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164142,
    "taskID" : "task_1369942127770_1206_m_000086",
    "taskType" : "MAP",
    "finishTime" : 1371222230895,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2115.smile.com" ]
      },
      "hostName" : "/default-rack/a2115.smile.com",
      "startTime" : 1371222212325,
      "result" : "SUCCESS",
      "finishTime" : 1371222230895,
      "attemptID" : "attempt_1369942127770_1206_m_000086_0",
      "clockSplits" : [ 1916, 1917, 1917, 1917, 1917, 1310, 628, 629, 629, 628, 1548, 3609 ],
      "cpuUsages" : [ 1298, 1299, 1299, 1298, 1299, 1114, 906, 906, 907, 906, 944, 1414 ],
      "vmemKbytes" : [ 129021, 387064, 645107, 903150, 1161194, 1390573, 1426632, 1426631, 1426632, 1426632, 1426631, 1426632 ],
      "physMemKbytes" : [ 27206, 81618, 136031, 190443, 244856, 293234, 300915, 301006, 301097, 301188, 300855, 298031 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13590,
        "virtualMemoryUsage" : 1460871168,
        "physicalMemoryUsage" : 304197632,
        "heapUsage" : 738656256
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72236,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222164142,
    "taskID" : "task_1369942127770_1206_m_000087",
    "taskType" : "MAP",
    "finishTime" : 1371222243205,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2115.smile.com" ]
      },
      "hostName" : "/default-rack/a2115.smile.com",
      "startTime" : 1371222212324,
      "result" : "SUCCESS",
      "finishTime" : 1371222243205,
      "attemptID" : "attempt_1369942127770_1206_m_000087_0",
      "clockSplits" : [ 2494, 2494, 2494, 2494, 1979, 18502, 69, 70, 70, 70, 70, 70 ],
      "cpuUsages" : [ 1501, 1501, 1501, 1501, 1135, 1230, 804, 803, 803, 804, 803, 804 ],
      "vmemKbytes" : [ 178670, 536012, 893354, 1250695, 1435451, 1432834, 1425524, 1425523, 1425524, 1425524, 1425523, 1425524 ],
      "physMemKbytes" : [ 38239, 114717, 191196, 267674, 307284, 303356, 292389, 292664, 292939, 293215, 293490, 293766 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13190,
        "virtualMemoryUsage" : 1459736576,
        "physicalMemoryUsage" : 300957696,
        "heapUsage" : 723517440
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164142,
    "taskID" : "task_1369942127770_1206_m_000088",
    "taskType" : "MAP",
    "finishTime" : 1371222232772,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2115.smile.com" ]
      },
      "hostName" : "/default-rack/a2115.smile.com",
      "startTime" : 1371222212326,
      "result" : "SUCCESS",
      "finishTime" : 1371222232772,
      "attemptID" : "attempt_1369942127770_1206_m_000088_0",
      "clockSplits" : [ 2652, 2652, 2653, 2652, 1643, 1624, 1581, 4351, 35, 35, 35, 35 ],
      "cpuUsages" : [ 1349, 1350, 1350, 1350, 1074, 1054, 836, 832, 804, 803, 804, 804 ],
      "vmemKbytes" : [ 177435, 532306, 887178, 1242049, 1424874, 1424915, 1424916, 1424915, 1424916, 1424916, 1424915, 1424916 ],
      "physMemKbytes" : [ 36160, 108480, 180800, 253120, 290481, 290705, 291974, 294028, 293783, 292064, 290343, 288624 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 12710,
        "virtualMemoryUsage" : 1459113984,
        "physicalMemoryUsage" : 294797312,
        "heapUsage" : 723648512
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164143,
    "taskID" : "task_1369942127770_1206_m_000089",
    "taskType" : "MAP",
    "finishTime" : 1371222232612,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2117.smile.com" ]
      },
      "hostName" : "/default-rack/a2117.smile.com",
      "startTime" : 1371222213322,
      "result" : "SUCCESS",
      "finishTime" : 1371222232612,
      "attemptID" : "attempt_1369942127770_1206_m_000089_0",
      "clockSplits" : [ 2150, 2150, 2150, 2150, 2150, 1793, 1344, 1294, 925, 926, 926, 1328 ],
      "cpuUsages" : [ 1229, 1229, 1229, 1229, 1229, 1163, 1080, 1055, 866, 865, 866, 1240 ],
      "vmemKbytes" : [ 129366, 388101, 646836, 905570, 1164305, 1397662, 1437812, 1437790, 1435927, 1432883, 1429838, 1427696 ],
      "physMemKbytes" : [ 27669, 83009, 138349, 193688, 249028, 299294, 310933, 314481, 312611, 307268, 301925, 298134 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 13280,
        "virtualMemoryUsage" : 1461878784,
        "physicalMemoryUsage" : 305094656,
        "heapUsage" : 740360192
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72236,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222164143,
    "taskID" : "task_1369942127770_1206_m_000090",
    "taskType" : "MAP",
    "finishTime" : 1371222235284,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2116.smile.com" ]
      },
      "hostName" : "/default-rack/a2116.smile.com",
      "startTime" : 1371222223387,
      "result" : "SUCCESS",
      "finishTime" : 1371222235284,
      "attemptID" : "attempt_1369942127770_1206_m_000090_0",
      "clockSplits" : [ 1105, 1106, 1106, 1106, 1105, 1106, 1106, 3584, 142, 142, 142, 143 ],
      "cpuUsages" : [ 1039, 1040, 1040, 1039, 1040, 1040, 1039, 1034, 799, 800, 800, 800 ],
      "vmemKbytes" : [ 89336, 268010, 446684, 625357, 804032, 982705, 1161380, 1339992, 1424680, 1424680, 1424679, 1424680 ],
      "physMemKbytes" : [ 18972, 56916, 94861, 132805, 170750, 208694, 246639, 284573, 305523, 311161, 316798, 322437 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 11510,
        "virtualMemoryUsage" : 1458872320,
        "physicalMemoryUsage" : 333062144,
        "heapUsage" : 662962176
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164143,
    "taskID" : "task_1369942127770_1206_m_000091",
    "taskType" : "MAP",
    "finishTime" : 1371222237538,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2115.smile.com" ]
      },
      "hostName" : "/default-rack/a2115.smile.com",
      "startTime" : 1371222223387,
      "result" : "SUCCESS",
      "finishTime" : 1371222237538,
      "attemptID" : "attempt_1369942127770_1206_m_000091_0",
      "clockSplits" : [ 1479, 1479, 1479, 1479, 1479, 1479, 1300, 625, 625, 625, 625, 1471 ],
      "cpuUsages" : [ 955, 956, 956, 955, 956, 956, 901, 694, 694, 694, 694, 1249 ],
      "vmemKbytes" : [ 104472, 313416, 522360, 731303, 940248, 1149191, 1353575, 1418951, 1418952, 1418952, 1418951, 1418952 ],
      "physMemKbytes" : [ 22666, 67999, 113332, 158665, 203998, 249331, 293651, 307068, 305951, 304834, 303716, 302630 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 10660,
        "virtualMemoryUsage" : 1453006848,
        "physicalMemoryUsage" : 309538816,
        "heapUsage" : 723517440
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164144,
    "taskID" : "task_1369942127770_1206_m_000092",
    "taskType" : "MAP",
    "finishTime" : 1371222239274,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2116.smile.com" ]
      },
      "hostName" : "/default-rack/a2116.smile.com",
      "startTime" : 1371222225390,
      "result" : "SUCCESS",
      "finishTime" : 1371222239274,
      "attemptID" : "attempt_1369942127770_1206_m_000092_0",
      "clockSplits" : [ 1029, 1029, 1030, 1029, 1030, 1029, 1030, 1029, 1030, 2434, 717, 718 ],
      "cpuUsages" : [ 945, 945, 945, 945, 945, 945, 945, 945, 945, 895, 730, 730 ],
      "vmemKbytes" : [ 72549, 217647, 362746, 507844, 652943, 798041, 943140, 1088238, 1233337, 1374501, 1417191, 1417192 ],
      "physMemKbytes" : [ 14112, 42336, 70560, 98783, 127008, 155231, 183456, 211680, 239905, 267439, 277716, 280513 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 11280,
        "virtualMemoryUsage" : 1451204608,
        "physicalMemoryUsage" : 288616448,
        "heapUsage" : 674430976
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72236,
    "outputRecords" : 4166666
  }, {
    "startTime" : 1371222164144,
    "taskID" : "task_1369942127770_1206_m_000093",
    "taskType" : "MAP",
    "finishTime" : 1371222249913,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2116.smile.com" ]
      },
      "hostName" : "/default-rack/a2116.smile.com",
      "startTime" : 1371222226395,
      "result" : "SUCCESS",
      "finishTime" : 1371222249913,
      "attemptID" : "attempt_1369942127770_1206_m_000093_0",
      "clockSplits" : [ 1998, 1998, 1998, 1999, 1998, 1998, 1999, 1998, 1998, 1998, 1999, 1533 ],
      "cpuUsages" : [ 852, 852, 853, 852, 853, 852, 853, 852, 852, 853, 852, 1494 ],
      "vmemKbytes" : [ 62260, 186780, 311301, 435821, 560342, 684863, 809384, 933904, 1058425, 1182946, 1307467, 1419006 ],
      "physMemKbytes" : [ 12360, 37081, 61802, 86523, 111244, 135965, 160686, 185407, 210128, 234849, 259570, 281661 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 10870,
        "virtualMemoryUsage" : 1471885312,
        "physicalMemoryUsage" : 291975168,
        "heapUsage" : 674562048
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164144,
    "taskID" : "task_1369942127770_1206_m_000094",
    "taskType" : "MAP",
    "finishTime" : 1371222241825,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2118.smile.com" ]
      },
      "hostName" : "/default-rack/a2118.smile.com",
      "startTime" : 1371222227397,
      "result" : "SUCCESS",
      "finishTime" : 1371222241825,
      "attemptID" : "attempt_1369942127770_1206_m_000094_0",
      "clockSplits" : [ 1436, 1437, 1437, 1437, 1437, 1436, 1437, 3845, 130, 129, 129, 130 ],
      "cpuUsages" : [ 1038, 1039, 1039, 1039, 1039, 1039, 1039, 1031, 891, 892, 892, 892 ],
      "vmemKbytes" : [ 90707, 272123, 453538, 634953, 816370, 997785, 1179201, 1360296, 1440536, 1440536, 1440535, 1440536 ],
      "physMemKbytes" : [ 19774, 59323, 98872, 138421, 177970, 217519, 257068, 296545, 313357, 312135, 310912, 309691 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666700,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166667,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166667,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 11870,
        "virtualMemoryUsage" : 1475108864,
        "physicalMemoryUsage" : 316497920,
        "heapUsage" : 723451904
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166667,
    "outputBytes" : 72236,
    "outputRecords" : 4166667
  }, {
    "startTime" : 1371222164145,
    "taskID" : "task_1369942127770_1206_m_000095",
    "taskType" : "MAP",
    "finishTime" : 1371222241726,
    "attempts" : [ {
      "location" : {
        "layers" : [ "default-rack", "a2118.smile.com" ]
      },
      "hostName" : "/default-rack/a2118.smile.com",
      "startTime" : 1371222228399,
      "result" : "SUCCESS",
      "finishTime" : 1371222241726,
      "attemptID" : "attempt_1369942127770_1206_m_000095_0",
      "clockSplits" : [ 1288, 1289, 1289, 1289, 1288, 1289, 1289, 1289, 2675, 112, 112, 112 ],
      "cpuUsages" : [ 992, 993, 993, 992, 993, 993, 992, 993, 853, 832, 832, 832 ],
      "vmemKbytes" : [ 88547, 265642, 442738, 619833, 796929, 974024, 1151119, 1328214, 1437170, 1435904, 1433127, 1430352 ],
      "physMemKbytes" : [ 19692, 59077, 98462, 137847, 177232, 216617, 256003, 295388, 318265, 314440, 310247, 306056 ],
      "shuffleFinished" : -1,
      "sortFinished" : -1,
      "hdfsBytesRead" : 86,
      "hdfsBytesWritten" : 416666600,
      "fileBytesRead" : 0,
      "fileBytesWritten" : 72236,
      "mapInputRecords" : 4166666,
      "mapOutputBytes" : -1,
      "mapOutputRecords" : 4166666,
      "combineInputRecords" : -1,
      "reduceInputGroups" : -1,
      "reduceInputRecords" : -1,
      "reduceShuffleBytes" : -1,
      "reduceOutputRecords" : -1,
      "spilledRecords" : 0,
      "mapInputBytes" : -1,
      "resourceUsageMetrics" : {
        "cumulativeCpuUsage" : 11290,
        "virtualMemoryUsage" : 1463259136,
        "physicalMemoryUsage" : 311255040,
        "heapUsage" : 740229120
      }
    } ],
    "preferredLocations" : [ ],
    "taskStatus" : "SUCCESS",
    "inputBytes" : 86,
    "inputRecords" : 4166666,
    "outputBytes" : 72236,
    "outputRecords" : 4166666
  } ],
  "reduceTasks" : [ ],
  "launchTime" : 1371222164048,
  "totalMaps" : 96,
  "totalReduces" : 0,
  "otherTasks" : [ ],
  "jobProperties" : {
    "mapreduce.job.ubertask.enable" : "false",
    "yarn.resourcemanager.max-completed-applications" : "10000",
    "yarn.resourcemanager.delayed.delegation-token.removal-interval-ms" : "30000",
    "mapreduce.client.submit.file.replication" : "2",
    "yarn.nodemanager.container-manager.thread-count" : "20",
    "mapred.queue.default.acl-administer-jobs" : "*",
    "dfs.image.transfer.bandwidthPerSec" : "0",
    "mapreduce.tasktracker.healthchecker.interval" : "60000",
    "mapreduce.jobtracker.staging.root.dir" : "/user",
    "yarn.resourcemanager.recovery.enabled" : "false",
    "yarn.resourcemanager.am.max-retries" : "1",
    "dfs.block.access.token.lifetime" : "600",
    "fs.AbstractFileSystem.file.impl" : "org.apache.hadoop.fs.local.LocalFs",
    "mapreduce.client.completion.pollinterval" : "5000",
    "mapreduce.job.ubertask.maxreduces" : "1",
    "mapreduce.reduce.shuffle.memory.limit.percent" : "0.25",
    "dfs.domain.socket.path" : "/var/run/hdfs-sockets/dn",
    "hadoop.ssl.keystores.factory.class" : "org.apache.hadoop.security.ssl.FileBasedKeyStoresFactory",
    "hadoop.http.authentication.kerberos.keytab" : "${user.home}/hadoop.keytab",
    "yarn.nodemanager.keytab" : "/etc/krb5.keytab",
    "io.seqfile.sorter.recordlimit" : "1000000",
    "s3.blocksize" : "********",
    "mapreduce.task.io.sort.factor" : "10",
    "yarn.nodemanager.disk-health-checker.interval-ms" : "120000",
    "mapreduce.job.working.dir" : "hdfs://a2115.smile.com:8020/user/jenkins",
    "yarn.admin.acl" : "*",
    "mapreduce.job.speculative.speculativecap" : "0.1",
    "dfs.namenode.num.checkpoints.retained" : "2",
    "dfs.namenode.delegation.token.renew-interval" : "86400000",
    "yarn.nodemanager.resource.memory-mb" : "8192",
    "io.map.index.interval" : "128",
    "s3.client-write-packet-size" : "65536",
    "mapreduce.task.files.preserve.failedtasks" : "false",
    "dfs.namenode.http-address" : "a2115.smile.com:20101",
    "ha.zookeeper.session-timeout.ms" : "5000",
    "hadoop.hdfs.configuration.version" : "1",
    "s3.replication" : "3",
    "dfs.datanode.balance.bandwidthPerSec" : "1048576",
    "mapreduce.reduce.shuffle.connect.timeout" : "180000",
    "hadoop.ssl.enabled" : "false",
    "dfs.journalnode.rpc-address" : "0.0.0.0:8485",
    "yarn.nodemanager.aux-services" : "mapreduce.shuffle",
    "mapreduce.job.counters.max" : "120",
    "dfs.datanode.readahead.bytes" : "4193404",
    "ipc.client.connect.max.retries.on.timeouts" : "45",
    "mapreduce.job.complete.cancel.delegation.tokens" : "true",
    "dfs.client.failover.max.attempts" : "15",
    "dfs.namenode.checkpoint.dir" : "file://${hadoop.tmp.dir}/dfs/namesecondary",
    "dfs.namenode.replication.work.multiplier.per.iteration" : "2",
    "fs.trash.interval" : "1",
    "yarn.resourcemanager.admin.address" : "a2115.smile.com:8033",
    "ha.health-monitor.check-interval.ms" : "1000",
    "mapreduce.job.outputformat.class" : "org.apache.hadoop.examples.terasort.TeraOutputFormat",
    "hadoop.jetty.logs.serve.aliases" : "true",
    "hadoop.http.authentication.kerberos.principal" : "HTTP/_HOST@LOCALHOST",
    "mapreduce.tasktracker.taskmemorymanager.monitoringinterval" : "5000",
    "mapreduce.job.reduce.shuffle.consumer.plugin.class" : "org.apache.hadoop.mapreduce.task.reduce.Shuffle",
    "s3native.blocksize" : "********",
    "dfs.namenode.edits.dir" : "${dfs.namenode.name.dir}",
    "ha.health-monitor.sleep-after-disconnect.ms" : "1000",
    "dfs.encrypt.data.transfer" : "false",
    "dfs.datanode.http.address" : "0.0.0.0:50075",
    "mapreduce.terasort.num-rows" : "400000000",
    "mapreduce.job.map.class" : "org.apache.hadoop.examples.terasort.TeraGen$SortGenMapper",
    "mapreduce.jobtracker.jobhistory.task.numberprogresssplits" : "12",
    "dfs.namenode.write.stale.datanode.ratio" : "0.5f",
    "dfs.client.use.datanode.hostname" : "false",
    "yarn.acl.enable" : "true",
    "hadoop.security.instrumentation.requires.admin" : "false",
    "yarn.nodemanager.localizer.fetch.thread-count" : "4",
    "hadoop.security.authorization" : "false",
    "user.name" : "jenkins",
    "dfs.namenode.fs-limits.min-block-size" : "1048576",
    "dfs.client.failover.connection.retries.on.timeouts" : "0",
    "hadoop.security.group.mapping.ldap.search.filter.group" : "(objectClass=group)",
    "mapreduce.output.fileoutputformat.compress.codec" : "org.apache.hadoop.io.compress.DefaultCodec",
    "dfs.namenode.safemode.extension" : "30000",
    "mapreduce.shuffle.port" : "8080",
    "mapreduce.reduce.log.level" : "INFO",
    "yarn.log-aggregation-enable" : "false",
    "dfs.datanode.sync.behind.writes" : "false",
    "mapreduce.jobtracker.instrumentation" : "org.apache.hadoop.mapred.JobTrackerMetricsInst",
    "dfs.https.server.keystore.resource" : "ssl-server.xml",
    "hadoop.security.group.mapping.ldap.search.attr.group.name" : "cn",
    "dfs.namenode.replication.min" : "1",
    "mapreduce.map.java.opts" : " -Xmx825955249",
    "yarn.scheduler.fair.allocation.file" : "/etc/yarn/fair-scheduler.xml",
    "s3native.bytes-per-checksum" : "512",
    "mapreduce.tasktracker.tasks.sleeptimebeforesigkill" : "5000",
    "tfile.fs.output.buffer.size" : "262144",
    "yarn.nodemanager.local-dirs" : "${hadoop.tmp.dir}/nm-local-dir",
    "mapreduce.jobtracker.persist.jobstatus.active" : "false",
    "fs.AbstractFileSystem.hdfs.impl" : "org.apache.hadoop.fs.Hdfs",
    "mapreduce.job.map.output.collector.class" : "org.apache.hadoop.mapred.MapTask$MapOutputBuffer",
    "mapreduce.tasktracker.local.dir.minspacestart" : "0",
    "dfs.namenode.safemode.min.datanodes" : "0",
    "hadoop.security.uid.cache.secs" : "14400",
    "dfs.client.https.need-auth" : "false",
    "dfs.client.write.exclude.nodes.cache.expiry.interval.millis" : "600000",
    "dfs.client.https.keystore.resource" : "ssl-client.xml",
    "dfs.namenode.max.objects" : "0",
    "hadoop.ssl.client.conf" : "ssl-client.xml",
    "dfs.namenode.safemode.threshold-pct" : "0.999f",
    "mapreduce.tasktracker.local.dir.minspacekill" : "0",
    "mapreduce.jobtracker.retiredjobs.cache.size" : "1000",
    "dfs.blocksize" : "134217728",
    "yarn.resourcemanager.scheduler.class" : "org.apache.hadoop.yarn.server.resourcemanager.scheduler.fifo.FifoScheduler",
    "mapreduce.job.reduce.slowstart.completedmaps" : "0.8",
    "mapreduce.job.end-notification.retry.attempts" : "5",
    "mapreduce.job.inputformat.class" : "org.apache.hadoop.examples.terasort.TeraGen$RangeInputFormat",
    "mapreduce.map.memory.mb" : "1024",
    "mapreduce.job.user.name" : "jenkins",
    "mapreduce.tasktracker.outofband.heartbeat" : "false",
    "io.native.lib.available" : "true",
    "mapreduce.jobtracker.persist.jobstatus.hours" : "0",
    "dfs.client-write-packet-size" : "65536",
    "mapreduce.client.progressmonitor.pollinterval" : "1000",
    "dfs.namenode.name.dir" : "file://${hadoop.tmp.dir}/dfs/name",
    "dfs.ha.log-roll.period" : "120",
    "mapreduce.reduce.input.buffer.percent" : "0.0",
    "mapreduce.map.output.compress.codec" : "org.apache.hadoop.io.compress.SnappyCodec",
    "mapreduce.map.skip.proc.count.autoincr" : "true",
    "dfs.client.failover.sleep.base.millis" : "500",
    "dfs.datanode.directoryscan.threads" : "1",
    "mapreduce.jobtracker.address" : "neededForHive:999999",
    "mapreduce.cluster.local.dir" : "${hadoop.tmp.dir}/mapred/local",
    "yarn.scheduler.fair.user-as-default-queue" : "true",
    "mapreduce.job.application.attempt.id" : "1",
    "dfs.permissions.enabled" : "true",
    "mapreduce.tasktracker.taskcontroller" : "org.apache.hadoop.mapred.DefaultTaskController",
    "yarn.scheduler.fair.preemption" : "true",
    "mapreduce.reduce.shuffle.parallelcopies" : "5",
    "dfs.support.append" : "true",
    "yarn.nodemanager.env-whitelist" : "JAVA_HOME,HADOOP_COMMON_HOME,HADOOP_HDFS_HOME,HADOOP_CONF_DIR,YARN_HOME",
    "mapreduce.jobtracker.heartbeats.in.second" : "100",
    "mapreduce.job.maxtaskfailures.per.tracker" : "3",
    "ipc.client.connection.maxidletime" : "10000",
    "mapreduce.shuffle.ssl.enabled" : "false",
    "dfs.namenode.invalidate.work.pct.per.iteration" : "0.32f",
    "dfs.blockreport.intervalMsec" : "21600000",
    "fs.s3.sleepTimeSeconds" : "10",
    "dfs.namenode.replication.considerLoad" : "true",
    "dfs.client.block.write.retries" : "3",
    "hadoop.ssl.server.conf" : "ssl-server.xml",
    "dfs.namenode.name.dir.restore" : "false",
    "rpc.engine.org.apache.hadoop.mapreduce.v2.api.MRClientProtocolPB" : "org.apache.hadoop.ipc.ProtobufRpcEngine",
    "dfs.datanode.hdfs-blocks-metadata.enabled" : "true",
    "ha.zookeeper.parent-znode" : "/hadoop-ha",
    "io.seqfile.lazydecompress" : "true",
    "dfs.https.enable" : "false",
    "mapreduce.reduce.merge.inmem.threshold" : "1000",
    "mapreduce.input.fileinputformat.split.minsize" : "0",
    "dfs.replication" : "3",
    "ipc.client.tcpnodelay" : "false",
    "dfs.namenode.accesstime.precision" : "3600000",
    "s3.stream-buffer-size" : "4096",
    "mapreduce.jobtracker.tasktracker.maxblacklists" : "4",
    "dfs.client.read.shortcircuit.skip.checksum" : "false",
    "mapreduce.job.jvm.numtasks" : "1",
    "mapreduce.task.io.sort.mb" : "100",
    "io.file.buffer.size" : "65536",
    "dfs.namenode.audit.loggers" : "default",
    "dfs.namenode.checkpoint.txns" : "1000000",
    "yarn.nodemanager.admin-env" : "MALLOC_ARENA_MAX=$MALLOC_ARENA_MAX",
    "mapreduce.job.jar" : "/user/jenkins/.staging/job_1369942127770_1206/job.jar",
    "mapreduce.job.split.metainfo.maxsize" : "10000000",
    "kfs.replication" : "3",
    "rpc.engine.org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolPB" : "org.apache.hadoop.ipc.ProtobufRpcEngine",
    "yarn.app.mapreduce.am.scheduler.heartbeat.interval-ms" : "1000",
    "mapreduce.reduce.maxattempts" : "4",
    "kfs.stream-buffer-size" : "4096",
    "dfs.ha.tail-edits.period" : "60",
    "hadoop.security.authentication" : "simple",
    "fs.s3.buffer.dir" : "${hadoop.tmp.dir}/s3",
    "rpc.engine.org.apache.hadoop.yarn.api.AMRMProtocolPB" : "org.apache.hadoop.ipc.ProtobufRpcEngine",
    "mapreduce.jobtracker.taskscheduler" : "org.apache.hadoop.mapred.JobQueueTaskScheduler",
    "yarn.app.mapreduce.am.job.task.listener.thread-count" : "30",
    "dfs.namenode.avoid.read.stale.datanode" : "false",
    "mapreduce.job.reduces" : "0",
    "mapreduce.map.sort.spill.percent" : "0.8",
    "dfs.client.file-block-storage-locations.timeout" : "60",
    "dfs.datanode.drop.cache.behind.writes" : "false",
    "mapreduce.job.end-notification.retry.interval" : "1",
    "mapreduce.job.maps" : "96",
    "mapreduce.job.speculative.slownodethreshold" : "1.0",
    "tfile.fs.input.buffer.size" : "262144",
    "mapreduce.map.speculative" : "false",
    "dfs.block.access.token.enable" : "false",
    "dfs.journalnode.http-address" : "0.0.0.0:8480",
    "mapreduce.job.acl-view-job" : " ",
    "mapreduce.reduce.shuffle.retry-delay.max.ms" : "60000",
    "mapreduce.job.end-notification.max.retry.interval" : "5",
    "ftp.blocksize" : "********",
    "mapreduce.tasktracker.http.threads" : "80",
    "mapreduce.reduce.java.opts" : " -Xmx825955249",
    "dfs.datanode.data.dir" : "file://${hadoop.tmp.dir}/dfs/data",
    "ha.failover-controller.cli-check.rpc-timeout.ms" : "20000",
    "dfs.namenode.max.extra.edits.segments.retained" : "10000",
    "dfs.https.port" : "20102",
    "dfs.namenode.replication.interval" : "3",
    "mapreduce.task.skip.start.attempts" : "2",
    "dfs.namenode.https-address" : "a2115.smile.com:20102",
    "mapreduce.jobtracker.persist.jobstatus.dir" : "/jobtracker/jobsInfo",
    "ipc.client.kill.max" : "10",
    "dfs.ha.automatic-failover.enabled" : "false",
    "mapreduce.jobhistory.keytab" : "/etc/security/keytab/jhs.service.keytab",
    "dfs.image.transfer.timeout" : "600000",
    "dfs.client.failover.sleep.max.millis" : "15000",
    "mapreduce.job.end-notification.max.attempts" : "5",
    "mapreduce.task.tmp.dir" : "./tmp",
    "dfs.default.chunk.view.size" : "32768",
    "kfs.bytes-per-checksum" : "512",
    "mapreduce.reduce.memory.mb" : "1024",
    "hadoop.http.filter.initializers" : "org.apache.hadoop.yarn.server.webproxy.amfilter.AmFilterInitializer",
    "dfs.datanode.failed.volumes.tolerated" : "0",
    "hadoop.http.authentication.type" : "simple",
    "dfs.datanode.data.dir.perm" : "700",
    "yarn.resourcemanager.client.thread-count" : "50",
    "ipc.server.listen.queue.size" : "128",
    "mapreduce.reduce.skip.maxgroups" : "0",
    "file.stream-buffer-size" : "4096",
    "dfs.namenode.fs-limits.max-directory-items" : "0",
    "io.mapfile.bloom.size" : "1048576",
    "yarn.nodemanager.container-executor.class" : "org.apache.hadoop.yarn.server.nodemanager.DefaultContainerExecutor",
    "mapreduce.map.maxattempts" : "4",
    "mapreduce.jobtracker.jobhistory.block.size" : "3145728",
    "yarn.log-aggregation.retain-seconds" : "-1",
    "yarn.app.mapreduce.am.job.committer.cancel-timeout" : "60000",
    "ftp.replication" : "3",
    "mapreduce.jobtracker.http.address" : "0.0.0.0:50030",
    "yarn.nodemanager.health-checker.script.timeout-ms" : "1200000",
    "mapreduce.jobhistory.address" : "a2115.smile.com:10020",
    "mapreduce.jobtracker.taskcache.levels" : "2",
    "dfs.datanode.dns.nameserver" : "default",
    "mapreduce.application.classpath" : "$HADOOP_MAPRED_HOME/share/hadoop/mapreduce/*,$HADOOP_MAPRED_HOME/share/hadoop/mapreduce/lib/*",
    "yarn.nodemanager.log.retain-seconds" : "10800",
    "mapred.child.java.opts" : "-Xmx200m",
    "dfs.replication.max" : "512",
    "map.sort.class" : "org.apache.hadoop.util.QuickSort",
    "dfs.stream-buffer-size" : "4096",
    "dfs.namenode.backup.address" : "0.0.0.0:50100",
    "hadoop.util.hash.type" : "murmur",
    "dfs.block.access.key.update.interval" : "600",
    "mapreduce.reduce.skip.proc.count.autoincr" : "true",
    "dfs.datanode.dns.interface" : "default",
    "dfs.datanode.use.datanode.hostname" : "false",
    "mapreduce.job.output.key.class" : "org.apache.hadoop.io.Text",
    "dfs.client.read.shortcircuit" : "false",
    "dfs.namenode.backup.http-address" : "0.0.0.0:50105",
    "yarn.nodemanager.container-monitor.interval-ms" : "3000",
    "yarn.nodemanager.disk-health-checker.min-healthy-disks" : "0.25",
    "kfs.client-write-packet-size" : "65536",
    "ha.zookeeper.acl" : "world:anyone:rwcda",
    "yarn.nodemanager.sleep-delay-before-sigkill.ms" : "250",
    "mapreduce.job.dir" : "/user/jenkins/.staging/job_1369942127770_1206",
    "io.map.index.skip" : "0",
    "net.topology.node.switch.mapping.impl" : "org.apache.hadoop.net.ScriptBasedMapping",
    "fs.s3.maxRetries" : "4",
    "dfs.namenode.logging.level" : "info",
    "ha.failover-controller.new-active.rpc-timeout.ms" : "60000",
    "s3native.client-write-packet-size" : "65536",
    "yarn.resourcemanager.amliveliness-monitor.interval-ms" : "1000",
    "hadoop.http.staticuser.user" : "dr.who",
    "mapreduce.reduce.speculative" : "false",
    "mapreduce.client.output.filter" : "FAILED",
    "mapreduce.ifile.readahead.bytes" : "4194304",
    "mapreduce.tasktracker.report.address" : "127.0.0.1:0",
    "mapreduce.task.userlog.limit.kb" : "0",
    "mapreduce.tasktracker.map.tasks.maximum" : "2",
    "hadoop.http.authentication.simple.anonymous.allowed" : "true",
    "hadoop.fuse.timer.period" : "5",
    "dfs.namenode.num.extra.edits.retained" : "1000000",
    "hadoop.rpc.socket.factory.class.default" : "org.apache.hadoop.net.StandardSocketFactory",
    "mapreduce.job.submithostname" : "a2115.smile.com",
    "dfs.namenode.handler.count" : "10",
    "fs.automatic.close" : "false",
    "mapreduce.job.submithostaddress" : "*************",
    "mapreduce.tasktracker.healthchecker.script.timeout" : "600000",
    "dfs.datanode.directoryscan.interval" : "21600",
    "yarn.resourcemanager.address" : "a2115.smile.com:8032",
    "yarn.nodemanager.health-checker.interval-ms" : "600000",
    "dfs.client.file-block-storage-locations.num-threads" : "10",
    "yarn.resourcemanager.container-tokens.master-key-rolling-interval-secs" : "86400",
    "mapreduce.reduce.markreset.buffer.percent" : "0.0",
    "hadoop.security.group.mapping.ldap.directory.search.timeout" : "10000",
    "mapreduce.map.log.level" : "INFO",
    "dfs.bytes-per-checksum" : "512",
    "yarn.nodemanager.localizer.address" : "0.0.0.0:8040",
    "dfs.namenode.checkpoint.max-retries" : "3",
    "ha.health-monitor.rpc-timeout.ms" : "45000",
    "yarn.resourcemanager.keytab" : "/etc/krb5.keytab",
    "ftp.stream-buffer-size" : "4096",
    "dfs.namenode.avoid.write.stale.datanode" : "false",
    "hadoop.security.group.mapping.ldap.search.attr.member" : "member",
    "mapreduce.output.fileoutputformat.outputdir" : "hdfs://a2115.smile.com:8020/user/jenkins/tera-gen-2",
    "dfs.blockreport.initialDelay" : "0",
    "yarn.nm.liveness-monitor.expiry-interval-ms" : "600000",
    "hadoop.http.authentication.token.validity" : "36000",
    "dfs.namenode.delegation.token.max-lifetime" : "604800000",
    "mapreduce.job.hdfs-servers" : "${fs.defaultFS}",
    "s3native.replication" : "3",
    "yarn.nodemanager.localizer.client.thread-count" : "5",
    "dfs.heartbeat.interval" : "3",
    "rpc.engine.org.apache.hadoop.ipc.ProtocolMetaInfoPB" : "org.apache.hadoop.ipc.ProtobufRpcEngine",
    "dfs.ha.fencing.ssh.connect-timeout" : "30000",
    "yarn.resourcemanager.container.liveness-monitor.interval-ms" : "600000",
    "yarn.am.liveness-monitor.expiry-interval-ms" : "600000",
    "mapreduce.task.profile" : "false",
    "mapreduce.tasktracker.http.address" : "0.0.0.0:50060",
    "mapreduce.tasktracker.instrumentation" : "org.apache.hadoop.mapred.TaskTrackerMetricsInst",
    "mapreduce.jobhistory.webapp.address" : "a2115.smile.com:19888",
    "ha.failover-controller.graceful-fence.rpc-timeout.ms" : "5000",
    "yarn.ipc.rpc.class" : "org.apache.hadoop.yarn.ipc.HadoopYarnProtoRPC",
    "mapreduce.job.name" : "TeraGen",
    "kfs.blocksize" : "********",
    "yarn.resourcemanager.am-rm-tokens.master-key-rolling-interval-secs" : "86400",
    "mapreduce.job.ubertask.maxmaps" : "9",
    "yarn.scheduler.maximum-allocation-mb" : "8192",
    "yarn.nodemanager.heartbeat.interval-ms" : "1000",
    "mapreduce.job.userlog.retain.hours" : "24",
    "dfs.namenode.secondary.http-address" : "0.0.0.0:50090",
    "mapreduce.task.timeout" : "600000",
    "mapreduce.framework.name" : "yarn",
    "ipc.client.idlethreshold" : "4000",
    "ftp.bytes-per-checksum" : "512",
    "ipc.server.tcpnodelay" : "false",
    "dfs.namenode.stale.datanode.interval" : "30000",
    "s3.bytes-per-checksum" : "512",
    "mapreduce.job.speculative.slowtaskthreshold" : "1.0",
    "yarn.nodemanager.localizer.cache.target-size-mb" : "10240",
    "yarn.nodemanager.remote-app-log-dir" : "/tmp/logs",
    "fs.s3.block.size" : "********",
    "mapreduce.job.queuename" : "sls_queue_1",
    "dfs.client.failover.connection.retries" : "0",
    "hadoop.rpc.protection" : "authentication",
    "yarn.scheduler.minimum-allocation-mb" : "1024",
    "yarn.app.mapreduce.client-am.ipc.max-retries" : "1",
    "hadoop.security.auth_to_local" : "DEFAULT",
    "dfs.secondary.namenode.kerberos.internal.spnego.principal" : "${dfs.web.authentication.kerberos.principal}",
    "ftp.client-write-packet-size" : "65536",
    "fs.defaultFS" : "hdfs://a2115.smile.com:8020",
    "yarn.nodemanager.address" : "0.0.0.0:0",
    "yarn.scheduler.fair.assignmultiple" : "true",
    "yarn.resourcemanager.scheduler.client.thread-count" : "50",
    "mapreduce.task.merge.progress.records" : "10000",
    "file.client-write-packet-size" : "65536",
    "yarn.nodemanager.delete.thread-count" : "4",
    "yarn.resourcemanager.scheduler.address" : "a2115.smile.com:8030",
    "fs.trash.checkpoint.interval" : "0",
    "hadoop.http.authentication.signature.secret.file" : "${user.home}/hadoop-http-auth-signature-secret",
    "s3native.stream-buffer-size" : "4096",
    "mapreduce.reduce.shuffle.read.timeout" : "180000",
    "mapreduce.admin.user.env" : "LD_LIBRARY_PATH=$HADOOP_COMMON_HOME/lib/native",
    "yarn.app.mapreduce.am.command-opts" : " -Xmx1238932873",
    "mapreduce.local.clientfactory.class.name" : "org.apache.hadoop.mapred.LocalClientFactory",
    "dfs.namenode.checkpoint.edits.dir" : "${dfs.namenode.checkpoint.dir}",
    "fs.permissions.umask-mode" : "022",
    "dfs.client.domain.socket.data.traffic" : "false",
    "hadoop.common.configuration.version" : "0.23.0",
    "mapreduce.tasktracker.dns.interface" : "default",
    "mapreduce.output.fileoutputformat.compress.type" : "BLOCK",
    "mapreduce.ifile.readahead" : "true",
    "hadoop.security.group.mapping.ldap.ssl" : "false",
    "io.serializations" : "org.apache.hadoop.io.serializer.WritableSerialization,org.apache.hadoop.io.serializer.avro.AvroSpecificSerialization,org.apache.hadoop.io.serializer.avro.AvroReflectSerialization",
    "yarn.nodemanager.aux-services.mapreduce.shuffle.class" : "org.apache.hadoop.mapred.ShuffleHandler",
    "fs.df.interval" : "60000",
    "mapreduce.reduce.shuffle.input.buffer.percent" : "0.70",
    "io.seqfile.compress.blocksize" : "1000000",
    "hadoop.security.groups.cache.secs" : "300",
    "ipc.client.connect.max.retries" : "10",
    "dfs.namenode.delegation.key.update-interval" : "86400000",
    "yarn.nodemanager.process-kill-wait.ms" : "2000",
    "yarn.application.classpath" : "$HADOOP_CONF_DIR,$HADOOP_COMMON_HOME/*,$HADOOP_COMMON_HOME/lib/*,$HADOOP_HDFS_HOME/*,$HADOOP_HDFS_HOME/lib/*,$HADOOP_MAPRED_HOME/*,$HADOOP_MAPRED_HOME/lib/*,$YARN_HOME/*,$YARN_HOME/lib/*",
    "yarn.app.mapreduce.client.max-retries" : "3",
    "dfs.datanode.available-space-volume-choosing-policy.balanced-space-preference-fraction" : "0.75f",
    "yarn.nodemanager.log-aggregation.compression-type" : "none",
    "hadoop.security.group.mapping.ldap.search.filter.user" : "(&(objectClass=user)(sAMAccountName={0}))",
    "yarn.nodemanager.localizer.cache.cleanup.interval-ms" : "600000",
    "dfs.image.compress" : "false",
    "mapred.mapper.new-api" : "true",
    "yarn.nodemanager.log-dirs" : "${yarn.log.dir}/userlogs",
    "dfs.namenode.kerberos.internal.spnego.principal" : "${dfs.web.authentication.kerberos.principal}",
    "fs.s3n.block.size" : "********",
    "fs.ftp.host" : "0.0.0.0",
    "hadoop.security.group.mapping" : "org.apache.hadoop.security.JniBasedUnixGroupsMappingWithFallback",
    "dfs.datanode.address" : "0.0.0.0:50010",
    "mapreduce.map.skip.maxrecords" : "0",
    "dfs.datanode.https.address" : "0.0.0.0:50475",
    "file.replication" : "1",
    "yarn.resourcemanager.resource-tracker.address" : "a2115.smile.com:8031",
    "dfs.datanode.drop.cache.behind.reads" : "false",
    "hadoop.fuse.connection.timeout" : "300",
    "hadoop.work.around.non.threadsafe.getpwuid" : "false",
    "mapreduce.jobtracker.restart.recover" : "false",
    "hadoop.tmp.dir" : "/tmp/hadoop-${user.name}",
    "mapreduce.output.fileoutputformat.compress" : "false",
    "mapreduce.tasktracker.indexcache.mb" : "10",
    "mapreduce.client.genericoptionsparser.used" : "true",
    "dfs.client.block.write.replace-datanode-on-failure.policy" : "DEFAULT",
    "mapreduce.job.committer.setup.cleanup.needed" : "true",
    "hadoop.kerberos.kinit.command" : "kinit",
    "dfs.datanode.du.reserved" : "0",
    "dfs.namenode.fs-limits.max-blocks-per-file" : "1048576",
    "dfs.webhdfs.enabled" : "false",
    "file.bytes-per-checksum" : "512",
    "mapreduce.task.profile.reduces" : "0-2",
    "mapreduce.jobtracker.handler.count" : "10",
    "dfs.client.block.write.replace-datanode-on-failure.enable" : "true",
    "mapreduce.job.output.value.class" : "org.apache.hadoop.io.Text",
    "yarn.dispatcher.exit-on-error" : "true",
    "net.topology.script.number.args" : "100",
    "mapreduce.task.profile.maps" : "0-2",
    "dfs.namenode.decommission.interval" : "30",
    "dfs.image.compression.codec" : "org.apache.hadoop.io.compress.DefaultCodec",
    "yarn.resourcemanager.webapp.address" : "a2115.smile.com:8088",
    "mapreduce.jobtracker.system.dir" : "${hadoop.tmp.dir}/mapred/system",
    "hadoop.ssl.hostname.verifier" : "DEFAULT",
    "yarn.nodemanager.vmem-pmem-ratio" : "2.1",
    "dfs.namenode.support.allow.format" : "true",
    "mapreduce.jobhistory.principal" : "jhs/<EMAIL>",
    "io.mapfile.bloom.error.rate" : "0.005",
    "mapreduce.shuffle.ssl.file.buffer.size" : "65536",
    "dfs.permissions.superusergroup" : "supergroup",
    "dfs.datanode.available-space-volume-choosing-policy.balanced-space-threshold" : "10737418240",
    "mapreduce.jobtracker.expire.trackers.interval" : "600000",
    "mapreduce.cluster.acls.enabled" : "false",
    "yarn.nodemanager.remote-app-log-dir-suffix" : "logs",
    "ha.failover-controller.graceful-fence.connection.retries" : "1",
    "ha.health-monitor.connect-retry-interval.ms" : "1000",
    "mapreduce.reduce.shuffle.merge.percent" : "0.66",
    "yarn.app.mapreduce.am.resource.mb" : "1536",
    "io.seqfile.local.dir" : "${hadoop.tmp.dir}/io/local",
    "dfs.namenode.checkpoint.check.period" : "60",
    "yarn.resourcemanager.nm.liveness-monitor.interval-ms" : "1000",
    "mapreduce.jobtracker.maxtasks.perjob" : "-1",
    "mapreduce.jobtracker.jobhistory.lru.cache.size" : "5",
    "file.blocksize" : "********",
    "tfile.io.chunk.size" : "1048576",
    "mapreduce.job.acl-modify-job" : " ",
    "yarn.nodemanager.webapp.address" : "0.0.0.0:8042",
    "mapreduce.tasktracker.reduce.tasks.maximum" : "2",
    "io.skip.checksum.errors" : "false",
    "mapreduce.cluster.temp.dir" : "${hadoop.tmp.dir}/mapred/temp",
    "yarn.app.mapreduce.am.staging-dir" : "/user",
    "dfs.namenode.edits.journal-plugin.qjournal" : "org.apache.hadoop.hdfs.qjournal.client.QuorumJournalManager",
    "dfs.datanode.handler.count" : "10",
    "fs.ftp.host.port" : "21",
    "dfs.namenode.decommission.nodes.per.interval" : "5",
    "yarn.resourcemanager.admin.client.thread-count" : "1",
    "dfs.namenode.fs-limits.max-component-length" : "0",
    "dfs.namenode.checkpoint.period" : "3600",
    "fs.AbstractFileSystem.viewfs.impl" : "org.apache.hadoop.fs.viewfs.ViewFs",
    "yarn.resourcemanager.resource-tracker.client.thread-count" : "50",
    "mapreduce.tasktracker.dns.nameserver" : "default",
    "mapreduce.map.output.compress" : "true",
    "dfs.datanode.ipc.address" : "0.0.0.0:50020",
    "hadoop.ssl.require.client.cert" : "false",
    "yarn.nodemanager.delete.debug-delay-sec" : "0",
    "dfs.datanode.max.transfer.threads" : "4096"
  },
  "computonsPerMapInputByte" : -1,
  "computonsPerMapOutputByte" : -1,
  "computonsPerReduceInputByte" : -1,
  "computonsPerReduceOutputByte" : -1,
  "heapMegabytes" : 200,
  "outcome" : "SUCCESS",
  "jobtype" : "JAVA",
  "directDependantJobs" : [ ],
  "successfulMapAttemptCDFs" : [ {
    "maximum" : 9223372036854775807,
    "minimum" : -9223372036854775808,
    "rankings" : [ ],
    "numberValues" : 0
  }, {
    "maximum" : 9223372036854775807,
    "minimum" : -9223372036854775808,
    "rankings" : [ ],
    "numberValues" : 0
  }, {
    "maximum" : 9223372036854775807,
    "minimum" : -9223372036854775808,
    "rankings" : [ ],
    "numberValues" : 0
  }, {
    "maximum" : 32847,
    "minimum" : 11897,
    "rankings" : [ {
      "datum" : 14151,
      "relativeRanking" : 0.05
    }, {
      "datum" : 15844,
      "relativeRanking" : 0.1
    }, {
      "datum" : 18656,
      "relativeRanking" : 0.15
    }, {
      "datum" : 18806,
      "relativeRanking" : 0.2
    }, {
      "datum" : 18840,
      "relativeRanking" : 0.25
    }, {
      "datum" : 18982,
      "relativeRanking" : 0.3
    }, {
      "datum" : 19314,
      "relativeRanking" : 0.35
    }, {
      "datum" : 19587,
      "relativeRanking" : 0.4
    }, {
      "datum" : 19703,
      "relativeRanking" : 0.45
    }, {
      "datum" : 19800,
      "relativeRanking" : 0.5
    }, {
      "datum" : 19965,
      "relativeRanking" : 0.55
    }, {
      "datum" : 20604,
      "relativeRanking" : 0.6
    }, {
      "datum" : 20892,
      "relativeRanking" : 0.65
    }, {
      "datum" : 21526,
      "relativeRanking" : 0.7
    }, {
      "datum" : 21911,
      "relativeRanking" : 0.75
    }, {
      "datum" : 22144,
      "relativeRanking" : 0.8
    }, {
      "datum" : 22539,
      "relativeRanking" : 0.85
    }, {
      "datum" : 23034,
      "relativeRanking" : 0.9
    }, {
      "datum" : 23601,
      "relativeRanking" : 0.95
    } ],
    "numberValues" : 96
  } ],
  "failedMapAttemptCDFs" : [ {
    "maximum" : 9223372036854775807,
    "minimum" : -9223372036854775808,
    "rankings" : [ ],
    "numberValues" : 0
  }, {
    "maximum" : 9223372036854775807,
    "minimum" : -9223372036854775808,
    "rankings" : [ ],
    "numberValues" : 0
  }, {
    "maximum" : 9223372036854775807,
    "minimum" : -9223372036854775808,
    "rankings" : [ ],
    "numberValues" : 0
  }, {
    "maximum" : 9223372036854775807,
    "minimum" : -9223372036854775808,
    "rankings" : [ ],
    "numberValues" : 0
  } ],
  "successfulReduceAttemptCDF" : {
    "maximum" : 9223372036854775807,
    "minimum" : -9223372036854775808,
    "rankings" : [ ],
    "numberValues" : 0
  },
  "failedReduceAttemptCDF" : {
    "maximum" : 9223372036854775807,
    "minimum" : -9223372036854775808,
    "rankings" : [ ],
    "numberValues" : 0
  },
  "mapperTriesToSucceed" : [ 1.0 ],
  "failedMapperFraction" : 0.0,
  "relativeTime" : 0,
  "clusterMapMB" : -1,
  "clusterReduceMB" : -1,
  "jobMapMB" : 200,
  "jobReduceMB" : 200
}
