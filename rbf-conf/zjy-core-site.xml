<configuration>
  <property>
    <name>configuration.service</name>
    <value>org.apache.hadoop.fs.NameServiceConfigurationService</value>
  </property>
  <property>
    <name>configuration.service.name.team.id</name>
    <value>CL7202</value>
  </property>
  <property>
    <name>dfs.blocksize</name>
    <value>256m</value>
  </property>
  <property>
    <name>dfs.federation.client.is-router-fs.zjyprc-hadoop</name>
    <value>true</value>
  </property>
  <property>
    <name>dfs.ha.namenodes.zjyprc-hadoop</name>
    <value>host0,host1,host2,host3,host4,host5,host6,host7,host8,host9,host10,host11,host12,host13,host14</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.zjyprc-hadoop.host6</name>
    <value>zjy-hadoop-prc-router-ct07.bj:12500</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.zjyprc-hadoop.host7</name>
    <value>zjy-hadoop-prc-router-ct08.bj:12500</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.zjyprc-hadoop.host4</name>
    <value>zjy-hadoop-prc-router-ct05.bj:12500</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.zjyprc-hadoop.host2</name>
    <value>zjy-hadoop-prc-router-ct03.bj:12500</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.zjyprc-hadoop.host3</name>
    <value>zjy-hadoop-prc-router-ct04.bj:12500</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.zjyprc-hadoop.host0</name>
    <value>zjy-hadoop-prc-router-ct01.bj:12500</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.zjyprc-hadoop.host1</name>
    <value>zjy-hadoop-prc-router-ct02.bj:12500</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.zjyprc-hadoop.host5</name>
    <value>zjy-hadoop-prc-router-ct06.bj:12500</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.zjyprc-hadoop.host8</name>
    <value>zjy-hadoop-prc-router-ct09.bj:12500</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.zjyprc-hadoop.host9</name>
    <value>zjy-hadoop-prc-router-ct10.bj:12500</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.zjyprc-hadoop.host10</name>
    <value>zjy-hadoop-prc-router-ct11.bj:12500</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.zjyprc-hadoop.host11</name>
    <value>zjy-hadoop-prc-router-ct12.bj:12500</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.zjyprc-hadoop.host12</name>
    <value>zjy-hadoop-prc-router-ct13.bj:12500</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.zjyprc-hadoop.host13</name>
    <value>zjy-hadoop-prc-router-ct14.bj:12500</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.zjyprc-hadoop.host14</name>
    <value>zjy-hadoop-prc-router-ct15.bj:12500</value>
  </property>
  <property>
    <name>dfs.client.failover.proxy.provider.zjyprc-hadoop</name>
    <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
  </property>
  <property>
    <name>dfs.nameservices</name>
    <value>zjyprc-hadoop</value>
  </property>
  <property>
    <name>dfs.namenode.kerberos.principal.pattern</name>
    <value>hdfs_{prc,srv,tst}/<EMAIL></value>
  </property>
  <property>
    <name>hadoop.security.authentication</name>
    <value>kerberos</value>
  </property>
  <property>
    <name>dfs.client.failover.random.order</name>
    <value>true</value>
  </property>
</configuration>
