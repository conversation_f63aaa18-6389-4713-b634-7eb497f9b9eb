<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet type="text/xsl" href="configuration.xsl"?>
<!--
  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License. See accompanying LICENSE file.
-->

<!-- Put site-specific property overrides in this file. -->

<configuration>

  <property>
    <name>dfs.client.failover.proxy.provider.tjwqstaging-hdd</name>
    <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
  </property>

  <property>
    <name>dfs.client.failover.proxy.provider.tjwqstaging-hdd-0</name>
    <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
  </property>

  <property>
    <name>dfs.client.failover.proxy.provider.tjwqstaging-hdd-1</name>
    <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
  </property>

  <property>
    <name>dfs.client.read.shortcircuit</name>
    <value>true</value>
  </property>

  <property>
    <name>dfs.client.read.shortcircuit.skip.auth</name>
    <value>true</value>
  </property>

  <property>
    <name>dfs.client.slow.log.threshold.ms</name>
    <value>10000</value>
  </property>

  <property>
    <name>dfs.datanode.kerberos.principal</name>
    <value>hdfs_tst/<EMAIL></value>
  </property>

  <property>
    <name>dfs.datanode.keytab.file</name>
    <value>/etc/hadoop/conf/hdfs_tst.keytab</value>
  </property>

  <property>
    <name>dfs.domain.socket.path</name>
    <value>/home/<USER>/app/hdfs/tjwqstaging-hdd/datanode/dn_socket</value>
  </property>

  <property>
    <name>dfs.federation.client.is-router-fs.tjwqstaging-hdd</name>
    <value>true</value>
  </property>

  <property>
    <name>dfs.ha.namenodes.tjwqstaging-hdd</name>
    <value>host0,host1,host2,host3</value>
  </property>

  <property>
    <name>dfs.ha.namenodes.tjwqstaging-hdd-0</name>
    <value>host0,host1</value>
  </property>

  <property>
    <name>dfs.ha.namenodes.tjwqstaging-hdd-1</name>
    <value>host0,host1</value>
  </property>

  <property>
    <name>dfs.namenode.kerberos.internal.spnego.principal</name>
    <value>HTTP/<EMAIL></value>
  </property>

  <property>
    <name>dfs.namenode.kerberos.principal</name>
    <value>hdfs_tst/<EMAIL></value>
  </property>

  <property>
    <name>dfs.namenode.keytab.file</name>
    <value>/etc/hadoop/conf/hdfs_tst.keytab</value>
  </property>

  <property>
    <name>dfs.namenode.rpc-address.tjwqstaging-hdd-0.host0</name>
    <value>tj-hadoop-staging-zk02.kscn:12200</value>
  </property>

  <property>
    <name>dfs.namenode.rpc-address.tjwqstaging-hdd-0.host1</name>
    <value>tj-hadoop-staging-zk03.kscn:12200</value>
  </property>

  <property>
    <name>dfs.namenode.rpc-address.tjwqstaging-hdd-1.host0</name>
    <value>tj-hadoop-staging-zk04.kscn:12210</value>
  </property>

  <property>
    <name>dfs.namenode.rpc-address.tjwqstaging-hdd-1.host1</name>
    <value>tj-hadoop-staging-zk05.kscn:12210</value>
  </property>

  <property>
    <name>dfs.namenode.rpc-address.tjwqstaging-hdd.host0</name>
    <value>tj-hadoop-staging-zk02.kscn:12500</value>
  </property>

  <property>
    <name>dfs.namenode.rpc-address.tjwqstaging-hdd.host1</name>
    <value>tj-hadoop-staging-zk03.kscn:12500</value>
  </property>

  <property>
    <name>dfs.namenode.rpc-address.tjwqstaging-hdd.host2</name>
    <value>tj-hadoop-staging-zk04.kscn:12500</value>
  </property>

  <property>
    <name>dfs.namenode.rpc-address.tjwqstaging-hdd.host3</name>
    <value>tj-hadoop-staging-zk05.kscn:12500</value>
  </property>

  <property>
    <name>dfs.nameservices</name>
    <value>tjwqstaging-hdd-0,tjwqstaging-hdd-1,tjwqstaging-hdd</value>
  </property>

  <property>
    <name>dfs.secondary.namenode.kerberos.internal.spnego.principal</name>
    <value>HTTP/<EMAIL></value>
  </property>

  <property>
    <name>dfs.secondary.namenode.kerberos.principal</name>
    <value>hdfs_tst/<EMAIL></value>
  </property>

  <property>
    <name>dfs.secondary.namenode.keytab.file</name>
    <value>/etc/hadoop/conf/hdfs_tst.keytab</value>
  </property>

  <property>
    <name>fs.AbstractFileSystem.hdfs.impl</name>
    <value>org.apache.hadoop.fs.FederatedHdfs</value>
  </property>

  <property>
    <name>fs.hdfs.impl</name>
    <value>org.apache.hadoop.hdfs.FederatedDFSFileSystem</value>
  </property>

  <property>
    <name>fs.viewfs.mounttable.tjwqstaging-hdd.link./</name>
    <value>hdfs://tjwqstaging-hdd-0/</value>
  </property>

  <property>
    <name>fs.viewfs.mounttable.tjwqstaging-hdd.link./tmp</name>
    <value>hdfs://tjwqstaging-hdd-1/tmp</value>
  </property>

  <property>
    <name>ha.zookeeper.quorum.tjwqstaging-hdd</name>
    <value>tjwqstaging.observer.zk.hadoop.srv:11000</value>
  </property>

  <property>
    <name>ha.zookeeper.quorum.tjwqstaging-hdd.host0</name>
    <value>tjwqstaging.zk.hadoop.srv:11000</value>
  </property>

  <property>
    <name>ha.zookeeper.quorum.tjwqstaging-hdd.host1</name>
    <value>tjwqstaging.zk.hadoop.srv:11000</value>
  </property>

</configuration>
