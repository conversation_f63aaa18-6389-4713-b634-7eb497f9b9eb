<configuration>
<property>
<name>hadoop.security.authentication</name>
<value>kerberos</value>
</property>
<property>
<name>configuration.service.preserve.user.conf</name>
<value>true</value>
</property>
<property>
<name>dfs.namenode.kerberos.principal.pattern</name>
<value>hdfs_{prc,srv,tst}/<EMAIL></value>
</property>                                     
<property>
<name>configuration.service</name>
<value>org.apache.hadoop.fs.NameServiceConfigurationService</value>
</property>
<property>
<name>configuration.service.name.team.id</name>
<value>CL7202</value>
</property>
</configuration>
