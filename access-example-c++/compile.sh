#file name: compile.sh
export JAVA_HOME="/opt/kingsoft/jdk/jdk1.8.0_211"
export HADOOP_ROOT=/home/<USER>/program/hdfs-example3/zjyprc-hadoop-hadoop-pack-2.6.0-mdh3.12-jre8-SNAPSHOT/
#export HADOOP_SRC_HOME="./hadoop-hdfs/"
export HADOOP_HOME="/home/<USER>/program/hdfs-example3/zjyprc-hadoop-hadoop-pack-2.6.0-mdh3.12-jre8-SNAPSHOT/" # CDH
export CXXFLAGS="-O2 -g -Wall -I$HADOOP_ROOT/include/ -I$JAVA_HOME/include/ -I$JAVA_HOME/include/linux/"
export DEPS_LIBS="-lhdfs -ljvm"
export LDFLAGS="-Wl,-rpath,/usr/local/lib -L/usr/local/lib -L$HADOOP_ROOT/lib/native/ -lhdfs -L$JAVA_HOME/jre/lib/amd64/server/ -ljvm"
export LD_LIBRARY_PATH=$HADOOP_ROOT/lib/native/:$JAVA_HOME/jre/lib/amd64/server/

g++ test.cpp -o a.out -g $CXXFLAGS $LDFLAGS $DEPS_LIBS
