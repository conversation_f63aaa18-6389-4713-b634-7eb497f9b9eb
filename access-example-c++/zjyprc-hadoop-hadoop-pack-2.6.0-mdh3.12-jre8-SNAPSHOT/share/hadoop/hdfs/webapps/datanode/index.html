<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="X-UA-Compatible" content="IE=9" />
<link rel="stylesheet" type="text/css" href="/static/bootstrap-3.0.2/css/bootstrap.min.css" />
<link rel="stylesheet" type="text/css" href="/static/hadoop.css" />
<title>DataNode Information</title>
</head>
<body>

<header class="navbar navbar-inverse bs-docs-nav" role="banner">
<div class="container">
  <div class="navbar-header">
    <div class="navbar-brand">Hadoop</div>
  </div>

  <ul class="nav navbar-nav" id="ui-tabs">
    <li><a>Overview</a></li>
  </ul>
</div>
</header>

<div class="container">

<div class="tab-content">
  <div class="tab-pane" id="tab-overview">
    <div class="page-header"><h1>DataNode on <small><div id="authority" style="display: inline-block"></div></small></h1></div>
  </div>
</div>

<div class="row">
  <hr />
  <div class="col-xs-2"><p>Hadoop, 2014.</p></div>
</div>
</div>

<script type="text/javascript" src="/static/jquery-1.10.2.min.js">
</script><script type="text/javascript" src="/static/bootstrap-3.0.2/js/bootstrap.min.js">
</script>
<script type="text/javascript">
$('#authority').html(window.location.host);
$('#tab-overview').addClass('active');
</script>
</body>
</html>
